{"dependencies": {"is-it-email": "^1.0.1", "katex": "^0.16.22", "monaco-editor": "^0.52.2", "nanoassert": "^3.0.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-katex": "patch:react-katex@npm%3A3.1.0#~/.yarn/patches/react-katex-npm-3.1.0-4c748a72e4.patch", "semantic-ui-react": "^3.0.0-beta.2", "socket.io-client": "patch:socket.io-client@npm%3A4.8.1#~/.yarn/patches/socket.io-client-npm-4.8.1-eb1c9f0ea4.patch", "tone": "^15.2.7", "zustand": "^5.0.7"}, "devDependencies": {"@hcaptcha/types": "^1.0.4", "@rollup/plugin-commonjs": "^28.0.6", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "@types/katex": "^0.16.7", "@types/nanoassert": "^2.0.3", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/react-katex": "^3.0.4", "@typescript-eslint/eslint-plugin": "^8.39.1", "esbuild": "^0.25.9", "eslint": "^9.33.0", "eslint-plugin-react": "^7.37.5", "magic-string": "^0.30.17", "rollup": "patch:rollup@npm%3A4.46.2#~/.yarn/patches/rollup-npm-4.46.2-14ffb3c832.patch", "rollup-plugin-esbuild": "patch:rollup-plugin-esbuild@npm%3A6.2.1#~/.yarn/patches/rollup-plugin-esbuild-npm-6.2.1-4a0cf9babd.patch", "typescript": "^5.9.2"}, "packageManager": "yarn@^4.9.2", "private": true, "scripts": {"build": "node build.ts", "dev": "NODE_ENV=development node build.ts", "prod": "NODE_ENV=production node build.ts", "clean": "rm -rf dist", "check": "tsc --noEmit", "lint": "eslint src"}, "type": "module"}