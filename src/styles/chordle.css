tr.chordle-list-loading.chordle-list-loading.chordle-list-loading.chordle-list-loading.chordle-list-loading {
	background: transparent !important;
	height: 4.5rem;
}

tr.chordle-list-loading>td {
	position: relative;
}

tr.chordle-list-none.chordle-list-none.chordle-list-none.chordle-list-none.chordle-list-none {
	background: transparent !important;
}

.active.row>.chord, .row>.chord:hover {
	background-color: #fdf;
	border: 1px solid rgba(34, 36, 38, .35);
}

.active.row>.level-0.chord, .row>.level-0.chord:hover { background-color: hsl(0, 100%, 85%); }
.active.row>.level-1.chord, .row>.level-1.chord:hover { background-color: hsl(10, 100%, 84%); }
.active.row>.level-2.chord, .row>.level-2.chord:hover { background-color: hsl(19, 100%, 83%); }
.active.row>.level-3.chord, .row>.level-3.chord:hover { background-color: hsl(29, 100%, 82%); }
.active.row>.level-4.chord, .row>.level-4.chord:hover { background-color: hsl(38, 100%, 81%); }
.active.row>.level-5.chord, .row>.level-5.chord:hover { background-color: hsl(48, 100%, 80%); }
.active.row>.level-6.chord, .row>.level-6.chord:hover { background-color: hsl(57, 100%, 79%); }
.active.row>.level-7.chord, .row>.level-7.chord:hover { background-color: hsl(67, 100%, 78%); }
.active.row>.level-8.chord, .row>.level-8.chord:hover { background-color: hsl(76, 100%, 77%); }
.active.row>.level-9.chord, .row>.level-9.chord:hover { background-color: hsl(86, 100%, 76%); }
.active.row>.correct.chord, .row>.correct.chord:hover { background-color: hsl(120, 100%, 75%); }
.active.row>.focused.chord, .row>.focused.chord:hover { background-color: #fec; }

.answer-area {
	margin-top: 2rem;
	padding-left: 50px;
}

.answer-area .title {
	font-weight: bolder;
}

.answer-area .title:before {
	content: '\300a';
}

.answer-area .title:after {
	content: '\300b';
}

.bars {
	display: flex;
	left: 0;
	position: absolute;
	top: 0;
	width: calc(var(--stage-width) + 50px);
}

.bars>.beat {
	flex: 1;
	padding-top: .64285714rem;
	overflow: hidden;
	text-align: center;
}

.bars>.brief.beat, .brief.bars>.detailed.beat {
	display: none;
}

.bars>.header {
	padding-top: .64285714rem;
	text-align: center;
}

.brief.bars>.brief.beat {
	display: block;
}

.chord-root {
	color: blue;
}

.chord-type {
	color: purple;
}

.chord.chord-type-correct .chord-type {
	color: #3c3;
	opacity: 1;
}

.chord.chord-type-present .chord-type {
	color: #fa3;
	opacity: 1;
}

.chord-type-major {
	opacity: .1;
	user-select: none;
}

.chordle-header {
	align-items: center;
	display: flex;
}

.chordle-header>.ui.breadcrumb {
	flex: 1;
}

.chordle-header>.ui.breadcrumb>.divider {
	margin: 0;
}

.chordle-header>.ui.icon.button {
	padding: .64285714em;
}

.dashboard {
	position: relative;
}

.dashboard>.line {
	border-top: 1.5px solid fuchsia;
	margin-left: 50px;
}

.demo-chord.demo-chord.demo-chord {
	display: inline-block;
	line-height: 20px;
	position: static;
	text-align: center;
	width: 64px;
}

.latency-test {
	display: flex;
}

.latency-test>canvas {
	border: 1px solid rgba(34, 36, 38, .15);
	border-radius: .28571429rem;
	flex: 1;
	height: 36px;
	margin-left: .5rem;
	width: 0;
}

.row {
	left: 0;
	position: absolute;
	width: 100%;
}

.row>.chord {
	background-color: #fef;
	border: 1px solid rgba(34, 36, 38, .15);
	border-radius: .28571429rem;
	display: flex;
	font-size: 1.28571429rem;
	justify-content: center;
	padding: .5rem 0;
	position: absolute;
}

.row>.chord>div {
	z-index: 1;
}

.row>.level-0.chord { background-color: hsl(0, 100%, 90%); }
.row>.level-1.chord { background-color: hsl(10, 100%, 89%); }
.row>.level-2.chord { background-color: hsl(19, 100%, 88%); }
.row>.level-3.chord { background-color: hsl(29, 100%, 87%); }
.row>.level-4.chord { background-color: hsl(38, 100%, 86%); }
.row>.level-5.chord { background-color: hsl(48, 100%, 85%); }
.row>.level-6.chord { background-color: hsl(57, 100%, 84%); }
.row>.level-7.chord { background-color: hsl(67, 100%, 83%); }
.row>.level-8.chord { background-color: hsl(76, 100%, 82%); }
.row>.level-9.chord { background-color: hsl(86, 100%, 81%); }
.row>.correct.chord { background-color: hsl(120, 100%, 80%); }

.row>.focused.chord { background-color: #fed; }

.row>.tight.chord {
	font-size: .85714286rem;
}

.row>.header {
	left: 7px;
	position: absolute;
}

.input-area {
	bottom: 2rem;
	display: flex;
	justify-content: center;
	position: fixed;
	z-index: 2;
}

.input-area>.ui.input {
	flex: 1;
	margin-right: .71428571rem;
}

.jkw {
	font-size: 1.28571429rem;
	position: fixed;
	top: -114514px;
	visibility: hidden;
}

.locked {
	cursor: not-allowed;
}

.progress-line {
	background-color: red;
	bottom: -.5rem;
	position: absolute;
	top: -.5rem;
	width: 1px;
	z-index: 2;
}

.progress-line.pending {
	background-color: rgba(255, 0, 0, .375);
}

.stage {
	display: flex;
}

.stage>.beat {
	flex: 1;
	flex-basis: 1.5px;
}

.stage>.header, .bars>.header {
	width: 50px;
}

.stage>.strong {
	border-left: 1.5px solid fuchsia;
}

.stage>.medium {
	border-left: 1.25px solid rgb(255, 0, 255, .375);
}

.stage>.weak {
	border-left: 1px solid rgba(255, 0, 255, .25);
}

.stat-today-id {
	color: #ccc;
	font-size: smaller;
}

.tonality-scale.major {
	color: green;
}

.tonality-scale.minor {
	color: purple;
}

@media only screen and (min-width: 768px) {
	.ui.form label.cspl {
		min-height: 26.7px;
	}
}
