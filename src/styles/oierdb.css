.contest-level {
	font-weight: bolder;
}

.contest-name:after {
	content: '|';
	margin-left: 5px;
	margin-right: 5px;
	display: inline-block;
	opacity: .3;
}

.progress-0 { color: hsl(0, 100%, 50%); }
.progress-1 { color: hsl(12, 100%, 48%); }
.progress-2 { color: hsl(24, 100%, 46%); }
.progress-3 { color: hsl(36, 100%, 44%); }
.progress-4 { color: hsl(48, 100%, 42%); }
.progress-5 { color: hsl(60, 100%, 40%); }
.progress-6 { color: hsl(72, 100%, 40%); }
.progress-7 { color: hsl(84, 100%, 40%); }
.progress-8 { color: hsl(96, 100%, 40%); }
.progress-9 { color: hsl(108, 100%, 40%); }
.progress-10 { color: hsl(120, 100%, 40%); }

.record-total {
	color: #ccc;
	font-size: smaller;
}

.stay-down {
	color: red;
}

.stay-down:after {
	content: '此记录为非正常年级，可能为该选手后期出现了留级等情况而导致的。';
}

.inconsistent-grade {
	color: fuchsia;
}

.inconsistent-grade:after {
	content: '此记录的年级与原始数据不一致，原始数据为 “' attr(data-grade) '”。';
}

.ui.transfer-to-cache.segment {
	align-items: center;
	display: flex;
	margin: 0;
	padding: .5em;
	position: absolute;
	right: 1.5rem;
}
