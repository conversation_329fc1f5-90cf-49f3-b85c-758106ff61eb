import { memory, _malloc } from './memory';

export interface Libcxx {
	_Znwm(count: number): number;
	_ZdlPvm(ptr: number, sz: number): number;
	_ZNSt3__112__next_primeEm(__n: number): number;
}

const $module = WebAssembly.compileStreaming(fetch(import.meta.resolve('libcxx')));

export const _libcxx = Promise.all([_malloc, $module])
	.then(([malloc, $module]) => WebAssembly.instantiate($module, { memory, malloc }))
	.then(({ exports }) => <Libcxx & WebAssembly.ModuleImports><unknown>exports);
