// corresponding to ld.so
// https://github.com/emscripten-core/emscripten/blob/main/src/lib/libdylink.js

import { _malloc } from './memory';
import { table } from './table';

function readLEB128(p: [Uint8Array]) {
	let i = 0, v = 0;
	for (; p[0][i] & 0x80; ++i)
		v |= (p[0][i] & 0x7f) << 7 * i;
	v |= p[0][i] << 7 * i;
	p[0] = p[0].subarray(i + 1);
	return v;
}

interface DataSize {
	memorySize: number;
	memoryAlignment: number;
	tableSize: number;
	tableAlignment: number;
}

/** See https://github.com/WebAssembly/tool-conventions/blob/main/DynamicLinking.md#the-dylink0-section. */
function getDynamicLinkingDataSize(module: WebAssembly.Module): DataSize {
	const
		[dyLink] = WebAssembly.Module.customSections(module, 'dylink.0'),
		a = new Uint8Array(dyLink), p: [Uint8Array] = [a.subarray(2)];
	if (a[0] !== 1 || a[1] !== a.length - 2) {
		throw new Error(`${module} is not a dynamic module`)
	}
	const
		memorySize = readLEB128(p),
		memoryAlignment = readLEB128(p),
		tableSize = readLEB128(p),
		tableAlignment = readLEB128(p);
	return { memorySize, memoryAlignment, tableSize, tableAlignment };
}

export async function load(module: WebAssembly.Module, imports: WebAssembly.Imports) {
	const
		{ memorySize, memoryAlignment, tableSize } = getDynamicLinkingDataSize(module),
		env = { __memory_base: (await _malloc).memalign(memoryAlignment, memorySize), __table_base: table.tp.value };
	table.tp.value += tableSize;
	return WebAssembly.instantiate(module, { ...imports, env });
}
