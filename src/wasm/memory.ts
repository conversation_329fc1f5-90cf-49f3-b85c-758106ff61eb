import { CONFIG } from './config';

export interface Memory {
	malloc(size: number): number;
	realloc(oldmem: number, bytes: number): number;
	memalign(alignment: number, bytes: number): number;
	free(mem: number): void;
}

export const memory = {
	global: new WebAssembly.Memory({ initial: (CONFIG.heapSize + CONFIG.stackSize + 0xffff) >>> 16 }),
	rel: new WebAssembly.Memory({ initial: (CONFIG.relSize + 0xffff) >>> 16 }),
	sp: new WebAssembly.Global({ value: 'i32', mutable: true }, CONFIG.heapSize + CONFIG.stackSize),
};

export const _malloc = WebAssembly.instantiateStreaming(
	fetch(import.meta.resolve('memory')),
	{ env: CONFIG, memory },
).then(({ instance: { exports } }) => <Memory & WebAssembly.ModuleImports><unknown>exports);
