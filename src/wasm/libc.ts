import { memory, _malloc } from './memory';

export interface Libc {
	memchr(ptr: number, ch: number, count: number): number;
	memcmp(lhs: number, rhs: number, count: number): number;
	relocate(mem: number, addend: number, pOffsets: number, count: number): void;
	strcmp(lhs: number, rhs: number): number;
	strlen(str: number): number;
	strncmp(lhs: number, rhs: number, count: number): number;
}

const $module = WebAssembly.compileStreaming(fetch(import.meta.resolve('libc')));

export const _libc = Promise.all([_malloc, $module])
	.then(([malloc, $module]) => WebAssembly.instantiate($module, { memory, malloc }))
	.then(({ exports }) => <Libc & WebAssembly.ModuleImports><unknown>exports);
