/** Initialize and render the page. */

import { createElement, StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { Container } from 'semantic-ui-react';

import Header from './components/util/Header';
import { RenderConfig } from './models/config';
import User, { init as initUserConfig } from './user';
import { init as initStorageConfig } from './util/config';

import Semantic from 'semantic@css' with { type: 'css' };
import Styles from './styles/main.css' with { type: 'css' };

function initOptimizeResize() {
	let running = false;
	const handler = function () {
		if (!running) {
			running = true;
			requestAnimationFrame(() => {
				window.dispatchEvent(new CustomEvent('RAFresize'));
				running = false;
			});
		}
	}
	window.addEventListener('resize', handler);
}

export function renderRoot(Component: React.FC, config?: RenderConfig) {
	document.adoptedStyleSheets.push(Seman<PERSON>, Styles);
	initOptimizeResize();
	initStorageConfig();
	initUserConfig();
	User.setInitConfig({ ...config?.initUser });
	User.initCurrentUser();
	createRoot(document.getElementById('root')!).render(
		<StrictMode>
			<Header />
			<Container className="main">
				<Component />
			</Container>
		</StrictMode>
	);
}
