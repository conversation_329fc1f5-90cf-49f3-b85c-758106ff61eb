import type { SemanticICONS } from 'semantic-ui-react';

export interface MenuItem {
	icon: SemanticICONS;
	name: string;
	to: string;
}

const Menu: Record<string, MenuItem> = {
	index: {
		icon: 'home',
		name: '首页',
		to: '/'
	},
	rot10451: {
		icon: 'sync',
		name: 'rot10451',
		to: '/rot10451'
	},
	chordle: {
		icon: 'music',
		name: 'Chordle',
		to: '/chordle'
	},
	faq: {
		icon: 'help',
		name: 'FAQ',
		to: '/help'
	}
};

export default Menu;
