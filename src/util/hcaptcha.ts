import Field from '../modules/form/field/Field';
import Rule from '../modules/form/rule/Rule';

export const
	HCAPTCHA_SITEKEY = 'effc4b1d-3ecc-4736-a3d9-950d9527f69c',
	HCAPTCHA_EXPIRED_MAGIC = '\0EXPIRED';

class NoCaptchaRule extends Rule {
	override validate(value: string) { return value !== ''; }
}

class ExpiredCaptchaRule extends Rule {
	override validate(value: string): boolean { return value !== HCAPTCHA_EXPIRED_MAGIC; }
}

export class HCaptchaField extends Field {
	constructor(name: string) {
		super(name);
		this
			.addRule(new NoCaptchaRule(() => '请完成 CAPTCHA 验证'))
			.addRule(new ExpiredCaptchaRule(() => '验证码已过期'));
	}
}
