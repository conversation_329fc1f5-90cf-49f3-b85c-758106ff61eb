import { isObject } from './type';

function getReplacer(ch: string) {
	const
		replacer = ([k, v]: [string, unknown]): [string, unknown] => [ch + k, v],
		comparator = ([k1]: [string, unknown], [k2]: [string, unknown]) => k1 < k2 ? -1 : k1 > k2 ? 1 : 0;
	return (_key: string, value: unknown) => isObject(value) ? Object.fromEntries(Object.entries(value).map(replacer).sort(comparator)) : value;
}
/**
 * 
 * Converts a JavaScript value to a sorted (fixed) JSON string.
 * @param value A JavaScript value, usually an object or array, to be converted.
 * @param space Adds indentation, white space, and line break characters to the return-value JSON text to make it easier to read.
 * @return The JSON string.
 */
export function sortedStringify(value: unknown, space?: string | number): string {
	let ch; const pre = JSON.stringify(value);
	for (let i = 0xfefe; pre.includes(ch = String.fromCharCode(i)); --i);
	return JSON.stringify(value, getReplacer(ch), space).replaceAll(ch, '');
}

/**
 * Safely parse a string into an object.
 * @param text A JSON string, may be not valid.
 * @return If the JSON is valid and it is an object, return [json, true], otherwise [{}, false]
 */
export function safeParse(text?: string | null): [object, boolean] {
	try {
		const obj = JSON.parse(text!);
		if (isObject(obj)) return [obj, true];
		throw new TypeError;
	} catch {
		return [{}, false];
	}
}
