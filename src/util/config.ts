import assert from 'nanoassert';
import { create, type StoreApi, type UseBoundStore } from 'zustand';

import type { Config, ConfigEntry } from '../models/config';
import { safeParse, sortedStringify } from './json';
import { isEmptyObject, isObject } from './type';

export type ConfigReducer<T> = (x: ConfigEntry | null) => T;

export class ConfigWithTime {
	config: Config;
	lastModified: Date;

	constructor(config: Config, lastModified: Date = new Date()) {
		this.config = config;
		this.lastModified = lastModified;
	}
}

const PREFERENCES_KEY = 'preferences';
const PREFERENCES_LAST_MODIFIED_KEY = PREFERENCES_KEY + 'LastModified';

function load(): ConfigWithTime {
	const config = <Config>safeParse(localStorage.getItem(PREFERENCES_KEY))[0];
	const lastModifiedRaw = localStorage.getItem(PREFERENCES_LAST_MODIFIED_KEY);
	let lastModified: Date;
	if (
		lastModifiedRaw == null ||
		isNaN(lastModifiedRaw) ||
		isNaN(lastModified = new Date(Number(lastModifiedRaw)))
	)
		lastModified = new Date(1);
	return new ConfigWithTime(config, lastModified);
}

function save(cwt: ConfigWithTime) {
	const configStr = sortedStringify(cwt.config);
	configStr === '{}' ? localStorage.removeItem(PREFERENCES_KEY) : localStorage.setItem(PREFERENCES_KEY, configStr);
	localStorage.setItem(PREFERENCES_LAST_MODIFIED_KEY, cwt.lastModified.getTime().toString());
}

/** `localStorage` config zustand store. */
export let ConfigCenter: UseBoundStore<StoreApi<ConfigWithTime>>;

export function init() {
	ConfigCenter = create(
		(set, get, api) => {
			assert(api.getState === get);
			assert(api.setState === set);
			api.setState = (cwt, replace = true, shouldSave = true) => {
				set(cwt, replace);
				if (shouldSave) save(<ConfigWithTime>cwt);
			};
			window.addEventListener('storage', event => {
				if (event.key === PREFERENCES_LAST_MODIFIED_KEY)
					set(load(), true);
			});
			return load();
		}
	);
}

/**
 * Use global config in react function component
 * @param path Config path similar to `lodash`'s interfaces form, e.g., ['rot10451', 'autoCopy'].
 * @param reducer Default value if not present (if `T`), or value transformer **even present** (if `ConfigReducer<T>`).
 */
export function useConfig<T>(path: string[]): [T | null, (next: T) => void];
export function useConfig<T>(path: string[], reducer: T | ConfigReducer<T>): [T, (next: T) => void];
export function useConfig<T>(path: string[], reducer: T | ConfigReducer<T> = <T>null): [T | null, (next: T) => void] {
	const
		configPath = new ConfigPath(path),
		value = ConfigCenter(cwt => configPath.get(cwt.config)),
		setValue = (value: T, removeEmpty = false) => {
			const current = ConfigCenter.getState().config;
			if (value == null && removeEmpty)
				configPath.delete(current);
			else
				configPath.set(current, <ConfigEntry>value);
			ConfigCenter.setState(new ConfigWithTime(current, new Date()));
		};

	return [
		typeof reducer === 'function'
			? (<ConfigReducer<T>>reducer)(value)
			: <T>(value ?? reducer),
		setValue
	];
}

/** A config wrapper with get/set/delete similar to `lodash.get/set/unset`, with automatic empty clear. */
export class ConfigPath {
	path: string[];
	last: string;

	constructor(path: string[]) {
		const last = path.pop();
		assert(last != null, 'The path must not be empty');
		this.last = last;
		this.path = path;
	}

	get(config: Config): ConfigEntry | null {
		for (const key of this.path) {
			if (!isObject(config[key])) {
				return null;
			}
			config = <Config>config[key];
		}
		return Object.hasOwn(config, this.last) ? config[this.last] : null;
	}

	set(config: Config, value: ConfigEntry) {
		for (const key of this.path) {
			if (!isObject(config[key])) {
				config[key] = {};
			}
			config = <Config>config[key];
		}
		config[this.last] = value;
	}

	delete(config: Config) {
		const stack: [Config, string][] = [];
		reset: {
			for (const key of this.path) {
				if (!Object.hasOwn(config, key)) {
					break reset;
				}
				if (!isObject(config[key])) {
					return;
				}
				stack.push([config, key]);
				config = <Config>config[key];
			}
			delete config[this.last];
		}
		for (const [config, key] of stack.reverse())
			if (isEmptyObject(config[key])) {
				delete config[key];
			} else {
				return;
			}
	}
}
