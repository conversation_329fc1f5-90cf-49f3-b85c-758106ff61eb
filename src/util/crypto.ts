import assert from 'nanoassert';

import { Buffer, FixedLengthBuffer } from '../buffer';
import type { AESEncryptedData } from '../models/crypto';
import User from '../user';
import { Zn } from './nt';

const { subtle } = crypto;
// errBuffer = Buffer.fromUtf8('解密失败，签名函数可能配置错误，<a href="/login">点此</a>重新配置');

/**
 * Encrypt/decrypt a short (fixed length) message (use symmetric directly).
 * @param message
 * @param rawKey
 * @returns CodecMessage.
 */
export function codecShortMessage(message: FixedLengthBuffer, rawKey: bigint) {
	const mask = BigInt('0x' + 'ff'.repeat(message.length));
	return FixedLengthBuffer.fromBigInt(
		message.asBigInt() ^ (rawKey & mask),
		message.length
	);
}

/**
 * Encrypt/decrypt a medium length message (use AES).
 * @param message Use uint8 array format.
 * @param ivKey Initial vector and key (use uint8 array format, initial vector are in high address / mod 2^16).
 * @param decrypt Whether it is decrypt.
 * @returns CodecMessage.
 */
export async function codecMessage(message: Buffer, ivKey: FixedLengthBuffer, decrypt = false) {
	assert(ivKey.length === 48, 'The length of (IV, key) should be 48');
	const
		ivKeyUint8 = ivKey.asUint8Array(),
		key = ivKeyUint8.subarray(0, 32), iv = ivKeyUint8.subarray(32, 48),
		aes = await subtle.importKey('raw', key, 'AES-CBC', true, ['encrypt', 'decrypt']);
	return Buffer.fromArrayBuffer(
		await (decrypt ? subtle.decrypt : subtle.encrypt).call(
			subtle, { name: 'AES-CBC', iv }, aes, message.asUint8Array()
		)
	);
}

export class EncryptedBundle {
	key: Buffer;
	data: AESEncryptedData;

	constructor(key: Buffer, data: AESEncryptedData) {
		this.key = key;
		this.data = data;
	}
}

export async function enAES(data: Buffer, user: User): Promise<EncryptedBundle> {
	const
		key = Zn(user.modulus.asBigInt()),
		key_e = user.encrypt(key),
		ivKey = FixedLengthBuffer.random(48),
		ivKey_e = codecShortMessage(ivKey, key),
		data_e = await codecMessage(data, ivKey, false);
	return new EncryptedBundle(
		Buffer.fromBigInt(key_e),
		{ ivKey: ivKey_e, data: data_e }
	);
}

export async function deAES(bundle: EncryptedBundle, user: User) {
	const ivKey_e = bundle.data.ivKey;
	assert(ivKey_e.length === 48, 'The length of (IV, key) should be 48');
	const
		data_e = bundle.data.data,
		key_e = bundle.key.asBigInt() % user.modulus.asBigInt(),
		key = await User.signFunc(key_e),
		ivKey = codecShortMessage(ivKey_e, key);
	return codecMessage(data_e, ivKey, true);
}

/**
 * Calculates the SHA3-256 hash of the given data.
 * @param data - The data to be hashed.
 * @returns The SHA3-256 hash of the given data.
 */
export function sha3_256(data: Uint8Array) {
	return sha3_256_raw().update(data).digest();
}
