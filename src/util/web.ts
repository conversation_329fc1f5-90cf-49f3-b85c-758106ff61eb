import { module<PERSON>ist, moduleTrie, type char } from 'module-trie';

// import Modals from '../modal/Modals.js';
import User, { UserCenter } from '../user';
import { ConfigCenter, ConfigPath, ConfigWithTime } from './config';
import type { Config } from '../models/config';

export const MAX_DATE = new Date(253402300799999); // 3093527980799999

/**
 * Wrapped `fetch` method with JSON response.
 * @param url This defines the resource that you wish to fetch.
 * @param options An object containing any custom settings that you want to apply to the request.
 * @returns A `Promise` that resolves to a `Response` JSON.
 */
export async function request(url: URL | RequestInfo, options?: RequestInit) {
	const response = await fetch(url, options);
	if (response.ok) return await response.json();
	throw new Error(
		`Request failed (status: ${response.status}${response.statusText ? ' ' + response.statusText : ''})`,
		{ cause: await response.json().then(x => x, () => response) },
	);
}

/**
 * 'GET' (retrieve) a message from backend.
 * @param url The url.
 * @returns Result.
 */
export function GET(url: URL | RequestInfo) { return request(url, {}) }

/**
 * 'POST' a message to backend.
 * @param url The url.
 * @param data The data to send.
 * @returns Result.
 */
export function POST(url: URL | RequestInfo, data: object) {
	return request(url, {
		method: 'POST',
		headers: { 'content-type': 'application/json; charset=utf-8' },
		body: JSON.stringify(data)
	});
}

/**
 * 'POST' a message with authentication data.
 * @param url The url.
 * @param data The data to send.
 * @param user The sender (identity).
 * @returns Result.
 */
export async function authPost(url: URL | RequestInfo, data: object, user?: User | null) {
	if (!user) user = UserCenter.getState();
	if (!user) return false;
	try {
		return await POST(url, await user.sign(data));
	} catch (e) {
		if (e instanceof Error && e.cause && Object.hasOwn(e.cause, 'auth')) {
			Object.assign(e.cause, { field: '', type: (e.cause as { auth: unknown }).auth });
		}
		throw e;
	}
}

/**
 * Downloads files on frontend with given data and filename.
 * @param data The blob data in file.
 * @param name The filename.
 */
export function plainDownload(data: BlobPart[], name: string) {
	const a = document.createElement('a');
	a.href = URL.createObjectURL(new Blob(data, { type: 'text/plain' }));
	a.download = name, a.style.display = 'none';
	document.body.appendChild(a), a.click(), URL.revokeObjectURL(a.href), a.remove();
}

/*
 * Get the resource URL based on the Ajax config user configured.
 * @param config The Ajax config user configured.
 * @param type The resource type.
 * @param entry The resource entry name.
 * @param defaultUrl The fallback URL.
 * @returns The resource URL.
// export function staticResolve(config: AjaxConfig, type: string, entry: string, defaultUrl: string) {
// 	const
// 		{ origin } = location,
// 		url: string = config.resources?.[type]?.[entry] || defaultUrl,
// 		base: string = config.generic?.[type] || config.generic?.base || '/ajax/libs/';

// 	let Url: URL, Base: URL;

// 	try { Base = new URL(base, origin); } catch { Base = new URL('/ajax/libs/', origin); }
// 	try { Url = url.startsWith('#') ? new URL(url.substring(1), origin) : new URL(url, Base); }
// 	catch { Url = defaultUrl.startsWith('#') ? new URL(defaultUrl.substring(1), origin) : new URL(defaultUrl, Base); }
// 	return Url.href.substring(Url.origin === origin ? origin.length : 0);
// }*/

const
	whiteListPath = new ConfigPath(['cookie', 'whiteList']),
	blackListPath = new ConfigPath(['cookie', 'blackList']);
/**
 * Checker whether to store cookie for this user.
 * @param uid uid of the user.
 * @returns The result whether to store.
 */
export function shouldSaveCookie(uid: string) {
	const { config } = ConfigCenter.getState();
	const whiteList = whiteListPath.get(config);
	if (Array.isArray(whiteList)) return whiteList.includes(uid);
	const blackList = blackListPath.get(config);
	if (Array.isArray(blackList)) return !blackList.includes(uid);
	return true;
}

/**
 * Adds the specified user ID to the cookie whitelist.
 * If the user ID is already in the whitelist, or blacklist is not empty, it will not be added.
 * @param uid - The user ID to add to the whitelist.
 */
export function addToWhitelist(uid: string) {
	const { config } = ConfigCenter.getState();
	const blackList = blackListPath.get(config);
	if (Array.isArray(blackList)) return;
	const whiteList_ = whiteListPath.get(config);
	const whiteList: string[] = Array.isArray(whiteList_) ? whiteList_ : [];
	if (!whiteList.includes(uid)) whiteList.push(uid);
	whiteListPath.set(config, whiteList as unknown as Config);
	ConfigCenter.setState(new ConfigWithTime(config, new Date()));
}

function resolveInternal(moduleId: string) {
	let x = moduleTrie;
	for (let i = 0; i < moduleId.length;) {
		if (x.su) {
			const r = moduleId.substring(i), s = x.su;
			if (r.length > s.length && r.startsWith(s)) {
				i += s.length;
			} else {
				break;
			}
		}
		const c: char = <char><unknown>moduleId[i++];
		if (!Object.hasOwn(x, c)) break;
		x = x[c];
	}
	return typeof x.vl === 'number' ? moduleList[x.vl] : null;
}
/**
 * Import a module at runtime.
 * @param moduleId The ID specifier of the module.
 * @returns The result module.
 */
export function runtimeImport(moduleId: string) {
	let id = moduleId.startsWith('./') ? moduleId.substring(2) : moduleId;
	if (id.startsWith('node_modules/')) id = id.substring(13);
	return import(resolveInternal(id) ?? moduleId);
}
