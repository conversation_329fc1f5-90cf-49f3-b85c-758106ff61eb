import { autoSpace } from '../../../util/string';
import IntRule from './IntRule';

export default class OddIntRule extends IntRule {
	protected override genAutoPrompt() {
		super.genAutoPrompt();
		const previous = this.prompt;
		this.prompt = (name, value) => {
			if (super.validate(value))
				return autoSpace(name, '必须为奇数');
			else
				return previous(name, value);
		}
	}

	override validate(value: string) {
		return super.validate(value) && Boolean(Number(value) & 1);
	}
}
