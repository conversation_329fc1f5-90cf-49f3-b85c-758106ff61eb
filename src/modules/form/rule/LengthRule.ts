import { autoSpace } from '../../../util/string';
import Rule, { type Prompt } from './Rule';

export default class LengthRule extends Rule {
	protected low: number;
	protected high: number;

	constructor(low: number, high: number, prompt?: Prompt) {
		super(prompt ?? (name => autoSpace(name, `的长度必须在 ${low} ~ ${high} 之间`)));
		this.low = low;
		this.high = high;
	}

	lower(low: number) {
		this.low = low;
		return this;
	}

	upper(high: number) {
		this.high = high;
		return this;
	}

	override validate(value: string) {
		return this.low <= value.length && value.length <= this.high;
	}
}
