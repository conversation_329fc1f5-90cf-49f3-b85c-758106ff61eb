import { autoSpace } from '../../../util/string';
import Rule, { type Prompt } from './Rule';

export default abstract class AbstractIntRule<T> extends Rule {
	protected low?: T;
	protected high?: T;
	protected autoPrompt: boolean;

	constructor(low?: T, high?: T, prompt?: Prompt) {
		super(prompt!);
		this.low = low;
		this.high = high;
		if ((this.autoPrompt = prompt == null))
			this.genAutoPrompt();
	}

	override setPrompt(prompt?: Prompt) {
		this.prompt = prompt!;
		if ((this.autoPrompt = prompt == null))
			this.genAutoPrompt();
		return this;
	}

	protected genAutoPrompt() {
		if (this.low == null && this.high == null)
			this.prompt = name => autoSpace(name, '必须为整数');
		else if (this.high == null)
			this.prompt = name => autoSpace(name, `不得小于 ${this.low}`);
		else if (this.low == null)
			this.prompt = name => autoSpace(name, `不得大于 ${this.high}`);
		else
			this.prompt = name => autoSpace(name, `必须为 ${this.low} ~ ${this.high} 之间的整数`);
	}

	lower(low: T) {
		this.low = low;
		if (this.autoPrompt)
			this.genAutoPrompt();
		return this;
	}

	upper(high: T) {
		this.high = high;
		if (this.autoPrompt)
			this.genAutoPrompt();
		return this;
	}
}
