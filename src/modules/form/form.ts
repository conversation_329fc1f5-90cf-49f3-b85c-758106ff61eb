import type { ReactNode } from 'react';
import type Field from './field/Field';

declare module 'zustand' {
	function useStore(form: Form): Record<string, string>;
}

export type ErrorMessage = ReactNode | ReactNode[];
export type MessageSupplier = ErrorMessage | ((...args: unknown[]) => ErrorMessage);

export default class Form {
	fields: Field[];
	fieldByName: Record<string, Field>;
	values: Record<string, string>;
	extraError: ReactNode[];
	extraErrorElement?: HTMLElement | null;
	extraErrorScroll: boolean;
	errorTable: Record<string, Record<string, MessageSupplier>>;
	protected listeners: Set<() => void>;

	constructor() {
		this.fields = [];
		this.fieldByName = {};
		this.values = {};
		this.errorTable = {};
		this.extraError = [];
		this.extraErrorScroll = false;
		this.listeners = new Set();

		this.getState = this.getState.bind(this);
		this.setState = this.setState.bind(this);
		this.subscribe = this.subscribe.bind(this);
		this.destroy = this.destroy.bind(this);
		this.handleChange = this.handleChange.bind(this);
	}

	addField(field: Field) {
		this.fields.push(field);
		this.fieldByName[field.name] = field;
		this.values[field.name] = '';
		return this;
	}

	withErrorTable(errorTable: Record<string, Record<string, MessageSupplier>>) {
		Object.assign(this.errorTable, errorTable);
		return this;
	}

	validate() {
		const ret = this.fields.map(field => field.validate(this.values[field.name])).includes(false);
		this.extraError = [];
		this.rerender();
		return !ret;
	}

	setExtraError(fieldName: string, error: ErrorMessage, args?: unknown[]) {
		const
			supplier = typeof error === 'string'
				? (this.errorTable?.[fieldName]?.[error] ?? this.errorTable?.[error] ?? error)
				: error!,
			message = typeof supplier === 'function' ? supplier(...(args ?? [])) : supplier,
			field = this.fieldByName[fieldName];
		if (field) {
			field.setExtraPrompt(Array.isArray(message) ? message[0] : message);
		} else {
			this.extraError = Array.isArray(message) ? message : [message];
			this.extraErrorScroll = true;
		}
		return this;
	}

	errorClass() {
		return this.extraError.length ? 'form error' : 'form';
	}

	errorScroll() {
		if (this.extraErrorScroll) {
			this.extraErrorElement?.scrollIntoView?.({ behavior: 'smooth', block: 'nearest' });
			this.extraErrorScroll = false;
		}
	}

	rerender() {
		this.values = { ...this.values };
		this.listeners.forEach(listener => listener());
	}

	getState() {
		return this.values;
	}

	setState(key: string, value: string) {
		if (this.values[key] !== value) {
			this.values[key] = value;
			this.rerender();
		}
	}

	subscribe(listener: () => void) {
		this.listeners.add(listener);
		return () => this.listeners.delete(listener);
	}

	destroy() {
		this.listeners.clear();
	}

	handleChange({ target }: { target: { name: string, value: string } }) {
		this.setState(target.name, target.value);
	}
}
