import { createElement, type ReactNode } from 'react';
import { FormField, Label, TextArea as RawTextArea, type TextAreaProps as RawTextAreaProps, type SemanticWIDTHS } from 'semantic-ui-react';

import type Form from '../form';

interface TextAreaProps extends RawTextAreaProps {
	readonly form: Form;
	readonly label: ReactNode;
	readonly name: string;
	readonly readOnly?: boolean;
	readonly width?: SemanticWIDTHS;
	readonly inline?: boolean;

	readonly small?: boolean;
	readonly tiny?: boolean;
}

const TextArea: React.FC<TextAreaProps> = props => {
	const rest: RawTextAreaProps = { ...props };
	delete rest.form;
	delete rest.label;
	delete rest.name;
	delete rest.readOnly;
	delete rest.small;
	delete rest.tiny;
	delete rest.width;

	let error = false, errorLabel = null;
	if (props.readOnly) {
		rest.placeholder ??= '(自动填充)';
	} else {
		const field = props.form.fieldByName[props.name];
		if (field.prompt) {
			error = true;
			errorLabel = <Label content={field.prompt} prompt pointing />;
		}
	}

	return (
		<FormField error={error} width={props.width} inline={props.inline}>
			<label>{props.label}</label>
			<RawTextArea
				className={props.small ? 'small' : props.tiny ? 'tiny' : ''}
				rows=""
				name={props.name}
				value={props.form.values[props.name]}
				readOnly={props.readOnly}
				onChange={props.readOnly ? null! : props.form.handleChange}
				{...rest}
			/>
			{errorLabel}
		</FormField>
	);
}

TextArea.displayName = 'TextArea';

export default TextArea;
