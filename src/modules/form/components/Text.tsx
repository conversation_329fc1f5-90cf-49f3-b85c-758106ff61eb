import { createElement, type HTMLProps, type ReactNode } from 'react';
import { FormField, Label, type SemanticWIDTHS } from 'semantic-ui-react';

import type Form from '../form';

type InputProps = Omit<HTMLProps<HTMLInputElement>, 'form' | 'label'>;

interface TextProps extends InputProps {
	readonly form: Form;
	readonly label: ReactNode;
	readonly name: string;
	readonly readOnly?: boolean;
	readonly width?: SemanticWIDTHS;
	readonly inline?: boolean;
}

const Text: React.FC<TextProps> = props => {
	const rest: InputProps = { ...props };
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	delete (rest as any).form;
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	delete (rest as any).label;
	delete rest.name;
	delete rest.readOnly;
	delete rest.width;

	let error = false, errorLabel = null;
	if (props.readOnly) {
		rest.placeholder ??= '(自动填充)';
	} else {
		const field = props.form.fieldByName[props.name];
		if (field.prompt) {
			error = true;
			errorLabel = <Label content={field.prompt} prompt pointing />;
		}
	}

	return (
		<FormField error={error} width={props.width} inline={props.inline}>
			<label>{props.label}</label>
			<input
				name={props.name}
				value={props.form.values[props.name]}
				readOnly={props.readOnly}
				onChange={props.readOnly ? null! : props.form.handleChange}
				{...rest}
			/>
			{errorLabel}
		</FormField>
	)
}

Text.displayName = 'Text';

export default Text;
