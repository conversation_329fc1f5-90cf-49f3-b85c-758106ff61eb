import { createElement } from 'react';
import { Message } from 'semantic-ui-react';

import type Form from '../form';

const FormErrorMessage: React.FC<{ readonly form: Form }> = props => {
	return (
		<Message
			error
			ref={ref => props.form.extraErrorElement = ref as unknown as HTMLElement}
		>
			<Message.List>
				{props.form.extraError.map((line, idx) => <Message.Item key={idx} content={line} />)}
			</Message.List>
		</Message>
	);
}

FormErrorMessage.displayName = 'FormErrorMessage';

export default FormErrorMessage;
