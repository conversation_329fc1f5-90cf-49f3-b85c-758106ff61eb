import { createElement, type ReactNode } from 'react';
import { FormField, Input, Label, type InputProps, type SemanticWIDTHS } from 'semantic-ui-react';

import type Form from '../form';

interface TextInputProps extends InputProps {
	readonly form: Form;
	readonly label: ReactNode;
	readonly name: string;
	readonly readOnly?: boolean;
	readonly width?: SemanticWIDTHS;
	readonly inline?: boolean;
}

const TextInput: React.FC<TextInputProps> = props => {
	const rest: InputProps = { ...props };
	delete rest.form;
	delete rest.label;
	delete rest.name;
	delete rest.readOnly;
	delete rest.width;

	let error = false, errorLabel = null;
	if (props.readOnly) {
		rest.placeholder ??= '(自动填充)';
	} else {
		const field = props.form.fieldByName[props.name];
		if (field.prompt) {
			error = true;
			errorLabel = <Label content={field.prompt} prompt pointing />;
		}
	}

	return (
		<FormField error={error} width={props.width} inline={props.inline}>
			<label>{props.label}</label>
			<Input
				name={props.name}
				value={props.form.values[props.name]}
				readOnly={props.readOnly}
				onChange={props.readOnly ? null! : props.form.handleChange}
				{...rest}
			/>
			{errorLabel}
		</FormField>
	)
}

TextInput.displayName = 'TextInput';

export default TextInput;
