import { regUID } from '../../../user';
import { autoSpace } from '../../../util/string';
import LengthRule from '../rule/LengthRule';
import RegExpRule from '../rule/RegExpRule';
import Field from './Field';

export default class UIDField extends Field {
	constructor(name: string, promptName = name) {
		super(name, promptName);
		this
			.addRule(new LengthRule(1, 20))
			.addRule(new RegExpRule(regUID, name => autoSpace(name, '只能包含大小写字母、数字、下划线 (_) 或连字符 (-)')));
	}
}
