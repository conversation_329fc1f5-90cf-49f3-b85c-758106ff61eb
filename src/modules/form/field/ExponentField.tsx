import { createElement, Fragment } from 'react';

import { autoSpace } from '../../../util/string';
import IntRule from '../rule/IntRule';
import OddIntRule from '../rule/OddIntRule';
import Field from './Field';

export default class ExponentField extends Field {
	constructor(name: string, promptName = name) {
		super(name, promptName);
		const rule = new OddIntRule(3, 4294967295, (name, value) => {
			if (IntRule.prototype.validate.call(rule, value))
				return autoSpace(name, '必须为奇数');
			else
				return <>
					{autoSpace(name, '必须为 3 ~ 2')}
					<sup>32</sup> - 1 之间的整数
				</>;
		})
		this.addRule(rule);
	}
}
