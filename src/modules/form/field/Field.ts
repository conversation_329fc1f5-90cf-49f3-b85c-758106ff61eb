import type { ReactNode } from 'react';

import type Rule from '../rule/Rule';

export default class Field {
	readonly name: string;
	promptName: string;
	rules: Rule[];
	optional: boolean;
	prompt: ReactNode;

	constructor(name: string, promptName = name) {
		this.name = name;
		this.promptName = promptName;
		this.rules = [];
		this.optional = false;
		this.prompt = '';
	}

	setPromptName(promptName: string) {
		this.promptName = promptName;
		return this;
	}

	addRule(rule: Rule) {
		this.rules.push(rule);
		return this;
	}

	setOptional() {
		this.optional = true;
		return this;
	}

	setExtraPrompt(prompt: ReactNode) {
		this.prompt = prompt;
	}

	validate(value: string) {
		value = value.trim();
		if (this.optional && !value.length) {
			this.prompt = null;
			return true;
		}
		for (const rule of this.rules)
			if (!rule.validate(value)) {
				this.prompt = rule.getPrompt()(this.promptName, value);
				return false;
			}
		this.prompt = null;
		return true;
	}
}
