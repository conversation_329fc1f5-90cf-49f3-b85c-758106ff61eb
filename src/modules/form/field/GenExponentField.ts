import { autoSpace } from '../../../util/string';
import Rule from '../rule/Rule';
import Field from './Field';

export default class GenExponentField extends Field {
	constructor(name: string, promptName = name) {
		super(name, promptName);
		this.addRule(new class extends Rule {
			override validate(value: string) {
				const number = Number(value);
				return number === 3 || number === 65537;
			}
		}(name => autoSpace(name, '必须为 3 或 65537，如需其它值请使用离线脚本')));
	}
}
