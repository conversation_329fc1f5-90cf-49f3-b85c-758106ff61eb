import { createElement, Fragment } from 'react';

import { autoSpace } from '../../../util/string';
import BigIntRule from '../rule/BigIntRule';
import Field from './Field';

const MODULUS_LIMIT = 2n ** 4096n - 1n;

export default class ModulusField extends Field {
	constructor(name: string, promptName = name) {
		super(name, promptName);
		this
			.addRule(new BigIntRule(1n, undefined, name => autoSpace(name, '必须为正整数')))
			.addRule(new BigIntRule(65537n))
			.addRule(new BigIntRule(0n, MODULUS_LIMIT, name => <>
				{autoSpace(name, '不得大于或等于 2')}
				<sup>4096</sup>
			</>));
	}
}
