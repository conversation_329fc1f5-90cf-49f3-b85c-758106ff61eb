import { autoSpace } from '../../../util/string';
import RegExpRule from '../rule/RegExpRule';
import Field from './Field';

const BASE64_L24 = /^[A-Za-z0-9+/=]{24}$/;

export default class VerifyCodeField extends Field {
	constructor(name: string, promptName = name) {
		super(name, promptName);
		this.addRule(new RegExpRule(BASE64_L24, name => autoSpace(name, '必须为合法的 24 位 Base64 串 (使用 + 和 / 而不是 - 和 _)')));
	}
}
