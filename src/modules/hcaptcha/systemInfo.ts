import { match, userAgent, type Candidate } from './commonInfo';

const OPERATING_SYSTEMS: Candidate[] = [
	{
		family: 'Windows',
		name_replace: 'Windows Phone',
		patterns: [
			'(Windows Phone) (?:OS[ /])?(\\d+)\\.(\\d+)',
			'^UCWEB.*; (wds) (\\d+)\\.(d+)(?:\\.(\\d+)|);',
			'^UCWEB.*; (wds) (\\d+)\\.(\\d+)(?:\\.(\\d+)|);',
		],
	},
	{
		family: 'Windows',
		name_replace: 'Windows Mobile',
		patterns: [
			'(Windows ?Mobile)',
		],
	},
	{
		name_replace: 'Android',
		patterns: [
			'(Android)[ \\-/](\\d+)(?:\\.(\\d+)|)(?:[.\\-]([a-z0-9]+)|)',
			'(Android) (d+);',
			'^UCWEB.*; (Adr) (\\d+)\\.(\\d+)(?:[.\\-]([a-z0-9]+)|);',
			'^(JUC).*; ?U; ?(?:Android|)(\\d+)\\.(\\d+)(?:[\\.\\-]([a-z0-9]+)|)',
			'(android)\\s(?:mobile\\/)(\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|)',
			'(Silk-Accelerated=[a-z]{4,5})',
			'Puffin/[\\d\\.]+AT',
			'Puffin/[\\d\\.]+AP',
		],
	},
	{
		name_replace: 'Chrome OS',
		patterns: [
			'(x86_64|aarch64)\\ (\\d+)\\.(\\d+)\\.(\\d+).*Chrome.*(?:CitrixChromeApp)$',
			'(CrOS) [a-z0-9_]+ (\\d+)\\.(\\d+)(?:\\.(\\d+)|)',
		],
	},
	{
		name_replace: 'Windows',
		patterns: [
			'(Windows 10)',
			'(Windows NT 6\\.4)',
			'(Windows NT 10\\.0)',
		],
		major_replace: '10',
	},
	{
		name_replace: 'Windows',
		patterns: [
			'(Windows NT 6\\.3; ARM;)',
			'(Windows NT 6.3)',
		],
		major_replace: '8',
		minor_replace: '1',
	},
	{
		name_replace: 'Windows',
		patterns: [
			'(Windows NT 6\\.2)',
		],
		major_replace: '8',
	},
	{
		name_replace: 'Windows',
		patterns: [
			'(Windows NT 6\\.1)',
		],
		major_replace: '7',
	},
	{
		name_replace: 'Windows',
		patterns: [
			'(Windows NT 6\\.0)',
		],
		major_replace: 'Vista',
	},
	{
		name_replace: 'Windows',
		patterns: [
			'(Windows (?:NT 5\\.2|NT 5\\.1))',
		],
		major_replace: 'XP',
	},
	{
		name_replace: 'Mac OS X',
		patterns: [
			'((?:Mac[ +]?|; )OS[ +]X)[\\s+/](?:(\\d+)[_.](\\d+)(?:[_.](\\d+)|)|Mach-O)',
			'\\w+\\s+Mac OS X\\s+\\w+\\s+(\\d+).(\\d+).(\\d+).*',
			'(?:PPC|Intel) (Mac OS X)',
		],
	},
	{
		name_replace: 'Mac OS X',
		patterns: [
			' (Dar)(win)/(10).(d+).*((?:i386|x86_64))',
		],
		major_replace: '10',
		minor_replace: '6',
	},
	{
		name_replace: 'Mac OS X',
		patterns: [
			' (Dar)(win)/(11).(\\d+).*\\((?:i386|x86_64)\\)',
		],
		major_replace: '10',
		minor_replace: '7',
	},
	{
		name_replace: 'Mac OS X',
		patterns: [
			' (Dar)(win)/(12).(\\d+).*\\((?:i386|x86_64)\\)',
		],
		major_replace: '10',
		minor_replace: '8',
	},
	{
		name_replace: 'Mac OS X',
		patterns: [
			' (Dar)(win)/(13).(\\d+).*\\((?:i386|x86_64)\\)',
		],
		major_replace: '10',
		minor_replace: '9',
	},
	{
		name_replace: 'iOS',
		patterns: [
			'^UCWEB.*; (iPad|iPh|iPd) OS (\\d+)_(\\d+)(?:_(\\d+)|);',
			'(CPU[ +]OS|iPhone[ +]OS|CPU[ +]iPhone|CPU IPhone OS)[ +]+(\\d+)[_\\.](\\d+)(?:[_\\.](\\d+)|)',
			'(iPhone|iPad|iPod); Opera',
			'(iPhone|iPad|iPod).*Mac OS X.*Version/(\\d+)\\.(\\d+)',
			'\\b(iOS[ /]|iOS; |iPhone(?:/| v|[ _]OS[/,]|; | OS : |\\d,\\d/|\\d,\\d; )|iPad/)(\\d{1,2})[_\\.](\\d{1,2})(?:[_\\.](\\d+)|)',
			'\\((iOS);',
			'(iPod|iPhone|iPad)',
			'Puffin/[\\d\\.]+IT',
			'Puffin/[\\d\\.]+IP',
		],
	},
	{
		family: 'Chrome',
		name_replace: 'Chromecast',
		patterns: [
			'(CrKey -)(?:[ /](\\d+)\\.(\\d+)(?:\\.(\\d+)|)|)',
			'(CrKey[ +]armv7l)(?:[ /](\\d+)\\.(\\d+)(?:\\.(\\d+)|)|)',
			'(CrKey)(?:[/](\\d+)\\.(\\d+)(?:\\.(\\d+)|)|)',
		],
	},
	{
		name_replace: 'Debian',
		patterns: [
			'([Dd]ebian)',
		],
	},
	{
		family: 'Linux',
		name_replace: 'Linux',
		patterns: [
			'(Linux Mint)(?:/(\\d+)|)',
		],
	},
	{
		family: 'Linux',
		patterns: [
			'(Ubuntu|Kubuntu|Arch Linux|CentOS|Slackware|Gentoo|openSUSE|SUSE|Red Hat|Fedora|PCLinuxOS|Mageia|(?:Free|Open|Net|\\b)BSD)',
			'(Mandriva)(?: Linux|)/(?:[\\d.-]+m[a-z]{2}(\\d+).(\\d)|)',
			'(Linux)(?:[ /](\\d+)\\.(\\d+)(?:\\.(\\d+)|)|)',
			'\\(linux-gnu\\)',
		],
	},
	{
		family: 'BlackBerry',
		name_replace: 'BlackBerry OS',
		patterns: ['(BB10);.+Version/(\\d+)\\.(\\d+)\\.(\\d+)',
			'(Black[Bb]erry)[0-9a-z]+/(\\d+)\\.(\\d+)\\.(\\d+)(?:\\.(\\d+)|)',
			'(Black[Bb]erry).+Version/(\\d+)\\.(\\d+)\\.(\\d+)(?:\\.(\\d+)|)',
			'(Black[Bb]erry)',
		],
	},
	{
		patterns: [
			'(Fedora|Red Hat|PCLinuxOS|Puppy|Ubuntu|Kindle|Bada|Sailfish|Lubuntu|BackTrack|Slackware|(?:Free|Open|Net|\\b)BSD)[/ ](\\d+)\\.(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)',
		],
	},
];

export class SystemInfo {
	mobile;
	os;
	version;

	constructor() {
		const os = match(userAgent, OPERATING_SYSTEMS);
		this.mobile =
			!!('ontouchstart' in window || navigator.maxTouchPoints > 0 || (<any>navigator).msMaxTouchPoints > 0) &&
			['iOS', 'Windows Phone', 'Windows Mobile', 'Android', 'BlackBerry OS'].includes(os.name);
		if (this.mobile && os && os.family === 'Windows' && !userAgent.includes('touch')) {
			this.mobile = false;
		}
		this.os = os.family === 'iOS' ? 'ios' :
			os.family === 'Android' ? 'android' :
				os.family === 'Mac OS X' ? 'mac' :
					os.family === 'Windows' ? 'windows' :
						os.family === 'Linux' ? 'linux' :
							os.family.toLowerCase();
		this.version = (() => {
			if (!os) return 'unknown';
			let version = os.major;
			if (os.minor) version += '.' + os.minor;
			if (os.patch) version += '.' + os.patch;
			return version;
		})();
	}

	dpr() { return window.devicePixelRatio ?? 1; }
}

export const System = new SystemInfo();
