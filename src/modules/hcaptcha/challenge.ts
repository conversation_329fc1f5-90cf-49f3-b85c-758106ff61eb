import { Browser } from './browserInfo';
import { createChat, removeChat } from './chat';
import { ASSET_URL } from './env';
import { makeQueryString } from './util';
import { getTable, translate } from './translator';
import { themeManager } from './themeManager';

export interface ChallengeConfig {
	['challenge-container']?: string | HTMLElement,
	size?: string,
}

export interface SetupConfig {
}

export class Challenge {
	id;
	config;
	top: number = null!;
	width: number = null!;
	height: number = null!;
	mobile = false;
	ready = false;
	listeners: (() => void)[] = [];
	_visible = false;
	_selected = false;
	$iframe;
	chat;
	_timeoutFailedToInitialize;
	_hasCustomContainer = false;
	_parent: HTMLElement = null!;
	$container;
	$wrapper;
	$overlay;
	$arrow
	$arrow_fg;
	$arrow_bg;

	constructor(id: string, config: ChallengeConfig) {
		this.id = id;
		this.config = config;
		this.$iframe = document.createElement('iframe');
		this.$iframe.src = `${ASSET_URL}/hcaptcha.html#frame=challenge&id=${id}&host=${window.location.hostname}&${makeQueryString(config)}`;
		(<any>this.$iframe).credentialless = true;
		(<any>this.$iframe).frameBorder = 0;
		(<any>this.$iframe).scrolling = 'no';
		this.setupParentContainer(config);
		this.chat = createChat(this.$iframe, id);
		this.chat.setReady(false);
		this._timeoutFailedToInitialize = setTimeout(() => { }, 60_000);
		if (Browser.supportsPST())
			this.$iframe.allow = "private-state-token-issuance 'src'; private-state-token-redemption 'src'";
		this.translate();
		if (this._hasCustomContainer) {
			this._hideIframe();
			this._parent.appendChild(this.$iframe);
			this.style();
		} else {
			this.$container = document.createElement('div');
			this.$wrapper = document.createElement('div');
			this.$overlay = document.createElement('div');
			this.$arrow = document.createElement('div');
			this.$arrow_fg = document.createElement('div');
			this.$arrow_bg = document.createElement('div');
			this.style();
			this.$arrow.append(this.$arrow_fg, this.$arrow_bg);
			this.$wrapper.appendChild(this.$iframe);
			this.$container.append(this.$wrapper, this.$overlay, this.$arrow);
			this._parent.appendChild(this.$container);
			this.$container.setAttribute('aria-hidden', 'true');
		}

		this.setData = this.setData.bind(this);
	}

	setupParentContainer(config: ChallengeConfig) {
		let container: HTMLElement | null = null;
		const c = config['challenge-container'];
		if (c) container = (typeof c === 'string' ? document.getElementById(c) : c);
		if (container) {
			this._hasCustomContainer = true;
			this._parent = container;
		} else {
			this._hasCustomContainer = false;
			this._parent = document.body;
		}
	}

	_hideIframe() {
		this.$iframe.setAttribute('aria-hidden', 'true');
		this.$iframe.style.opacity = '0';
		this.$iframe.style.visibility = 'hidden';
	}

	_showIframe() {
		this.$iframe.removeAttribute('aria-hidden');
		this.$iframe.style.opacity = '1';
		this.$iframe.style.visibility = 'visible';
	}

	style() {
		const { palette, component } = themeManager.get();
		const theme = themeManager.merge({
			main: { fill: palette.common.white, border: palette.grey[400] }
		},
			component.challenge,
		);
		if (this._hasCustomContainer) {
			this.$iframe.style.border = '0';
			this.$iframe.style.position = 'relative';
			this.$iframe.style.backgroundColor = theme.main.fill;
		} else {
			this.$container!.style.backgroundColor = theme.main.fill;
			this.$container!.style.border = `1px solid ${theme.main.border}`;
			this.$container!.style.boxShadow = 'rgba(0, 0, 0, 0.1) 0px 0px 4px';
			this.$container!.style.borderRadius = '4px';
			this.$container!.style.left = 'auto';
			this.$container!.style.top = '-10000';
			this.$container!.style.zIndex = '-9999999999999';
			this.$container!.style.position = 'absolute';
			this.$container!.style.pointerEvents = 'auto';
			this.$container!.style.transition = 'opacity 0.15s ease-out';
			this.$container!.style.opacity = '0';
			this.$container!.style.visibility = 'hidden';
			this.$wrapper!.style.position = 'relative';
			this.$wrapper!.style.zIndex = '1';
			this.$overlay!.style.width = '100%';
			this.$overlay!.style.height = '100%';
			this.$overlay!.style.position = 'fixed';
			this.$overlay!.style.pointerEvents = 'none';
			this.$overlay!.style.top = '0';
			this.$overlay!.style.left = '0';
			this.$overlay!.style.zIndex = '0';
			this.$overlay!.style.backgroundColor = theme.main.fill;
			this.$overlay!.style.opacity = '.05';
			this.$arrow!.style.borderWidth = '11';
			this.$arrow!.style.position = 'absolute';
			this.$arrow!.style.pointerEvents = 'none';
			this.$arrow!.style.marginTop = '-11';
			this.$arrow!.style.zIndex = '1';
			this.$arrow!.style.right = '100%';
			this.$arrow_fg!.style.borderWidth = '10';
			this.$arrow_fg!.style.borderStyle = 'solid';
			this.$arrow_fg!.style.borderColor = 'transparent rgb(255, 255, 255) transparent transparent';
			this.$arrow_fg!.style.position = 'relative';
			this.$arrow_fg!.style.top = '10';
			this.$arrow_fg!.style.zIndex = '1';
			this.$arrow_bg!.style.borderWidth = '11';
			this.$arrow_bg!.style.borderStyle = 'solid';
			this.$arrow_bg!.style.borderColor = `transparent ${theme.main.border} transparent transparent`;
			this.$arrow_bg!.style.position = 'relative';
			this.$arrow_bg!.style.top = '-11';
			this.$arrow_bg!.style.zIndex = '0';
			this.$iframe.style.border = '0',
				this.$iframe.style.zIndex = '2000000000';
			this.$iframe.style.position = 'relative';
		}
	}

	setup(config: SetupConfig) {
		return this.chat.send('create-challenge', config)
	}

	sendTranslation(locale: string) {
		if (this.chat) this.chat.send('challenge-translate', { locale, table: getTable(locale) ?? {} });
		this.translate();
	}

	translate() {
		this.$iframe.title = translate('Main content of the hCaptcha challenge')
	}

	isVisible() {
		return this._visible
	};

	setDimensions(width: number, height: number) {
		if (!this._visible) return null;
		return this.chat.contact('resize-challenge', { width, height });
	};

	show() {
		if (!this._visible) {
			this._visible = true;
			if (this._hasCustomContainer)
				this._showIframe();
			else {
				this.$container!.style.zIndex = '9999999999999';
				this.$container!.style.display = 'block';
				this.$container!.style.opacity = '1';
				this.$container!.style.visibility = 'visible';
				this.$container!.removeAttribute('aria-hidden');
				this.$overlay!.style.pointerEvents = 'auto';
				this.$overlay!.style.cursor = 'pointer';
			}
		}
	}

	focus() {
		this.$iframe.focus();
	};

	close(event?: unknown) {
		if (this._visible !== false) {
			this._visible = false;
			if (this._hasCustomContainer) {
				this._hideIframe();
				this.chat.send('close-challenge', { event });
				return;
			}
			this.$container!.style.left = 'auto';
			this.$container!.style.top = '-10000';
			this.$container!.style.zIndex = '-9999999999999';
			this.$container!.style.opacity = '0';
			this.$container!.style.visibility = 'hidden';
			this.$overlay!.style.pointerEvents = 'none';
			this.$overlay!.style.cursor = 'default';
			this.chat.send('close-challenge', { event });
			this.$container!.setAttribute('aria-hidden', 'true');
		}
	};

	size(width: number, height: number, mobile: boolean) {
		this.width = width;
		this.height = height;
		this.mobile = mobile;
		this.$iframe.width = `${width}px`;
		this.$iframe.height = `${height}px`;
		if (!this._hasCustomContainer) {
			this.$wrapper!.style.width = `${width}px`;
			this.$wrapper!.style.height = `${width}px`;
			this.$overlay!.style.opacity = mobile ? '.5' : '.05';
		}
	}

	position(data: any) {
		if (!this._hasCustomContainer && data) {
			const margin = 10,
				documentElement = window.document.documentElement,
				scrollY = Browser.scrollY(),
				width = Browser.width(),
				height = Browser.height(),
				isScrolled = Math.round(data.bounding.top) + scrollY !== data.offset.top;
			let
				shouldCenterHorizontally = this.mobile || 'invisible' === this.config.size || data.offset.left + data.tick.x <= data.tick.width / 2,
				left = shouldCenterHorizontally ? (width - this.width) / 2 : data.bounding.left + data.tick.right + 10;
			if (left + this.width + margin > width || left < 0) {
				left = (width - this.width) / 2;
				shouldCenterHorizontally = true;
			}
			const maxTop = (documentElement.scrollHeight < documentElement.clientHeight ? documentElement.clientHeight : documentElement.scrollHeight) - this.height - margin
			let top = shouldCenterHorizontally ? (height - this.height) / 2 + scrollY : data.bounding.top + data.tick.y + scrollY - this.height / 2;
			if (isScrolled && top < scrollY) top = scrollY + margin;
			if (isScrolled && top + this.height >= scrollY + height)
				top = scrollY + height - (this.height + margin);
			top = Math.max(Math.min(top, maxTop), 10);
			let arrowTop = data.bounding.top + data.tick.y + scrollY - top - 10, maxArrowTop = this.height - 10 - 30;
			arrowTop = Math.max(Math.min(arrowTop, maxArrowTop), margin);
			this.$container!.style.left = left;
			this.$container!.style.top = <any>top;
			this.$arrow_fg!.style.display = shouldCenterHorizontally ? 'none' : 'block';
			this.$arrow_bg!.style.display = shouldCenterHorizontally ? 'none' : 'block';
			this.$arrow!.style.top = <any>arrowTop;
			this.top = top;
			this.$container!.getBoundingClientRect();
		}
	}

	destroy() {
		if (this._timeoutFailedToInitialize) {
			clearTimeout(this._timeoutFailedToInitialize);
			this._timeoutFailedToInitialize = 0;
		}
		if (this._visible) this.close();
		removeChat(this.chat);
		this.chat.destroy();
		if (this._hasCustomContainer)
			this.$iframe.remove();
		else
			this.$container!.remove();
	};

	setReady() {
		if (this._timeoutFailedToInitialize) {
			clearTimeout(this._timeoutFailedToInitialize);
			this._timeoutFailedToInitialize = 0;
		}
		if (this.chat) this.chat.setReady(true);
		this.ready = true;
		for (; this.listeners.length; this.listeners.pop()!());
	};

	onReady(callback: (...args: unknown[]) => void, ...args: unknown[]) {
		const curried = () => callback(...args);
		this.ready ? curried() : this.listeners.push(curried);
	};

	onOverlayClick(callback: (e: PointerEvent) => void) {
		if (!this._hasCustomContainer)
			this.$overlay?.addEventListener('click', callback);
	}

	setData(data: any) {
		if (this.chat) this.chat.send('challenge-data', data)
	}
}
