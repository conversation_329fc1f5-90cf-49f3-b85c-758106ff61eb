import { Browser } from './browserInfo';
import type { Checkbox } from './checkbox';
import { errorInvalidID, errorMissingCaptcha, INVALID_CAPTCHA_ID, MISSING_CAPTCHA, MISSING_SITEKEY } from './errors';
import { CHALLENGE_ESCAPED } from './event';
import { add, each, getById, getByIndex, getSessions, Node, pushSession, remove as removeNode, updateLocale, updateTranslation } from './node';
import { recorder } from './recorder';
import { System } from './systemInfo';
import { getLocale, loadLocale, setLocale } from './translator';

function appendWidget(container: HTMLElement, customPrompt?: string) {
	container.style.width = '304px';
	container.style.height = '78px';
	container.style.backgroundColor = '#f9e5e5';
	container.style.position = 'relative';
	container.innerHTML = '';
	const div = document.createElement('div');
	div.style.width = '284px';
	div.style.position = 'absolute';
	div.style.top = '12px';
	div.style.left = '10px';
	div.style.color = '#7c0a06';
	div.style.fontSize = '14px';
	div.style.fontWeight = 'normal';
	div.style.lineHeight = '18px';
	div.innerHTML = customPrompt ?? "Please <a style=\"color: inherit; text-decoration: underline; font: inherit\" target=\"_blank\" href=\"https://www.whatismybrowser.com/guides/how-to-update-your-browser/auto\">upgrade your browser</a> to complete this captcha.";
	container.appendChild(div);
}

let nonce = 0;

export function render(container: HTMLElement | string, config: ConfigRender) {
	if (typeof container === 'string')
		container = document.getElementById(container)!;
	if (container && container.nodeType === 1) {
		let challengeContainer = config['challenge-container'], flag = true;
		if (challengeContainer) {
			if (typeof challengeContainer === 'string')
				challengeContainer = document.getElementById(challengeContainer)!;
			flag = challengeContainer && challengeContainer.nodeType === 1;
		}
		if (flag) {
			const
				id = nonce++ + Math.random().toString(36).substring(2),
				configR = {
					sentry: true,
					reportapi: 'https://accounts.hcaptcha.com',
					recaptchacompat: true,
					custom: false,
					hl: getLocale(),
					tplinks: 'on',
					pat: 'on',
					pstissuer: 'https://pst-issuer.hcaptcha.com',
					theme: 'light',
					origin: window.location.origin,
					...config,
				};
			let node: Node;
			try {
				node = new Node(container, id, configR);
			} catch (err: any) {
				let prompt = 'Your browser plugins or privacy policies are blocking the hCaptcha service. Please disable them for hCaptcha.com';
				if (err.cause === MISSING_SITEKEY) {
					prompt = 'hCaptcha has failed to initialize. Please see the developer tools console for more information.';
				}
				console.error(err.message);
				appendWidget(container, prompt);
				return;
			}
			if (config.callback) (node.onPass = <any>config.callback);
			if (config['expired-callback']) (node.onExpire = <any>config['expired-callback']);
			if (config['chalexpired-callback']) (node.onChalExpire = <any>config['chalexpired-callback']);
			if (config['open-callback']) (node.onOpen = <any>config['open-callback']);
			if (config['close-callback']) (node.onClose = <any>config['close-callback']);
			if (config['error-callback']) (node.onError = <any>config['error-callback']);
			try {
				recorder.setData('inv', configR.size === 'invisible');
				recorder.setData('size', configR.size);
				recorder.setData('theme', configR.theme);
				recorder.setData('pel', (container.outerHTML ?? '').replace(container.innerHTML, ''));
			} catch { }
			if (configR.size !== 'invisible') {
				(<Checkbox>node.checkbox).chat.listen('checkbox-selected', e => {
					const n = 'enter' === e.action ? 'kb' : 'm';
					recorder.setData('exec', n);
					node.onReady(node.initChallenge, e);
				});
				(<Checkbox>node.checkbox).chat.listen('checkbox-loaded', e => {
					node.checkbox.location.bounding = <any>node.checkbox.getBounding();
					node.checkbox.location.tick = e;
					node.checkbox.location.offset = <any>node.checkbox.getOffset();
					node.checkbox.sendTranslation(configR.hl);
				});
				if (configR.mode === 'auto')
					node.onReady(() => execute(node.id), configR);
			}
			node.challenge.chat.listen('site-setup', siteConfig => {
				const promise = node.setSiteConfig(siteConfig);
				node.challenge.onReady(() => promise.then(() => node.setReady(true)));
			});
			node.challenge.chat.listen("challenge-loaded", () => {
				node.challenge.setReady();
				node.challenge.sendTranslation(configR.hl);
			});
			node.challenge.chat.answer('challenge-ready', ((challenge: any, w: any) => node.displayChallenge(challenge)!.then(w.resolve)));
			node.challenge.chat.listen('challenge-resize', () => node.resize(Browser.width(), Browser.height()));
			node.challenge.chat.listen('challenge-closed', reason => {
				recorder.setData('lpt', Date.now());
				node.closeChallenge(reason);
			});
			node.challenge.chat.answer('get-url', e => e.resolve(window.location.href));
			node.challenge.chat.answer('getcaptcha-manifest', e => e.resolve(node.getGetCaptchaManifest()));
			node.challenge.chat.answer('check-api', e => e.resolve(recorder.getData()));
			node.challenge.chat.listen('challenge-key', e => pushSession(e.key, node.id));
			node.challenge.onOverlayClick(() => node.closeChallenge({ event: CHALLENGE_ESCAPED }));
			node.challenge.chat.listen('challenge-language', ((e: { locale: string }, t: boolean) => updateLocale(e, t, node)));
			updateLocale({ locale: configR.hl }, true, node);
			node.challenge.chat.answer('get-ac', e => e.resolve(false));
			add(node);
			return id;
		} else
			console.log(`[hCaptcha] render: invalid challenge container '${challengeContainer}'.`);
	} else
		console.log(`[hCaptcha] render: invalid container '${container}'.`);
}

export function reset(id: string) {
	const node = id ? getById(id) : getByIndex(0);
	if (node) {
		return node.reset();
	}
	throw id ? errorInvalidID(id) : errorMissingCaptcha();
}

export function remove(id: string) {
	const node = id ? getById(id) : getByIndex(0);
	if (node) {
		removeNode(node);
		node.destroy();
	}
	throw id ? errorInvalidID(id) : errorMissingCaptcha();
}

export function execute(id?: string, config?: ConfigExecute) {
	if (typeof id === 'object' && !config) config = id, id = null!;
	config ??= {};
	let node;
	const isAsync = config.async === true, { promise, resolve, reject } = Promise.withResolvers<HCaptchaResponse>();
	if (node = id ? getById(id) : getByIndex(0)) {
		recorder.setData('exec', 'api');
		if (isAsync)
			node.setPromise({ resolve, reject });
		node.onReady(node.initChallenge, config);
	} else if (id) {
		if (!isAsync)
			throw errorInvalidID(id);
		reject(INVALID_CAPTCHA_ID)
	} else {
		if (!isAsync)
			throw errorMissingCaptcha();
		reject(MISSING_CAPTCHA)
	}
	if (isAsync)
		return promise;
}

export function getResponse(id: string) {
	const node = id ? getById(id) : getByIndex(0);
	if (node) {
		return node.checkbox.response ?? '';
	}
	throw id ? errorInvalidID(id) : errorMissingCaptcha();
}

export function getRespKey(id: string) {
	const node = id ? getById(id) : getByIndex(0);
	try {
		const session = getSessions();
		for (let i = session.length; --i >= 0;)
			if (session[i][1] === node!.id)
				return session[i][0];
	} catch { }
	return '';
}

export function close(id: string) {
	const node = id ? getById(id) : getByIndex(0);
	if (node) {
		return node.closeChallenge({ event: CHALLENGE_ESCAPED });
	}
	throw id ? errorInvalidID(id) : errorMissingCaptcha();
}

export function setData(id: string, config: ConfigSetData) {
	if (typeof id === 'object' && !config) config = id, id = null!;
	if (typeof config !== 'object')
		throw Error('[hCaptcha] invalid data supplied');
	const node = id ? getById(id) : getByIndex(0);
	if (node) {
		return node.onReady(node.challenge.setData, config);
	}
	throw id ? errorInvalidID(id) : errorMissingCaptcha();
}

let aspectRatio = Browser.width() / Browser.height()
function resizeHandler() {
	const newRatio = Browser.width() / Browser.height(), updated = System.mobile && aspectRatio !== newRatio;
	aspectRatio = newRatio;
	resizeInner();
	each(node => node.visible && node.resize(Browser.width(), Browser.height(), updated));
}

function scrollHandler() {
	scrollInner();
	each(node => node.visible && node.position());
}

function scrollInner() {
	recorder.circBuffPush('xy', [Browser.scrollX(), Browser.scrollY(), document.documentElement.clientWidth / Browser.width(), Date.now()])
}

function resizeInner() {
	recorder.circBuffPush('wn', [Browser.width(), Browser.height(), System.dpr(), Date.now()])
}

setLocale(window.navigator.language);

{
	const locale = getLocale();
	if (locale !== 'en') {
		loadLocale(locale).then(() => each(node => updateTranslation(node, locale)));
	}
}

try {
	recorder.record();
	recorder.setData('sc', Browser.getScreenDimensions());
	recorder.setData('wi', Browser.getWindowDimensions());
	recorder.setData('nv', Browser.interrogateNavigator());
	recorder.setData('dr', document.referrer);
	resizeInner();
	scrollInner();
} catch { }

window.addEventListener('resize', resizeHandler);
window.addEventListener('scroll', scrollHandler);
