export const userAgent = navigator.userAgent;

export interface Candidate {
	family?: string,
	name_replace?: string,
	major_replace?: string,
	minor_replace?: string,
	patch_replace?: string,
	patterns: string[],
}

function matchVersion(string: string, pattern: string) {
	try {
		const mat = new RegExp(pattern).exec(string);
		if (mat) {
			return {
				name: mat[1] ?? 'Other',
				major: mat[2] ?? '0',
				minor: mat[3] ?? '0',
				patch: mat[4] ?? '0',
			};
		}
	} catch { }
	return null;
}

export function match(agent: string, candidates: Candidate[]) {
	for (const candidate of candidates) {
		for (const pattern of candidate.patterns) {
			const ver = matchVersion(agent, pattern);
			if (ver) {
				return {
					...ver,
					family: candidate.family || candidate.name_replace || ver.name,
					...(candidate.name_replace && { name: candidate.name_replace }),
					...(candidate.major_replace && { major: candidate.major_replace }),
					...(candidate.minor_replace && { minor: candidate.minor_replace }),
					...(candidate.patch_replace && { patch: candidate.patch_replace }),
				};
			}
		}
	}
	return {
		family: 'Other',
		name: 'Other',
		major: '0',
		minor: '0',
		patch: '0',
	};
}
