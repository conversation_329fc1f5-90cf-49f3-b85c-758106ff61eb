export type Listener = (...payload: any) => void;

export interface Message {
	label: string,
	listeners: Listener[],
}

export interface Waiting {
	label: string,
	reject: (reason: any) => void,
	resolve: (value: unknown) => void,
	lookup: string,
}

export interface Response {
	id: string,
	label: string,
	lookup: string,
	error?: string,
	contents?: string,
	promise?: 'create' | 'resolve' | 'reject',
}

export class Chat {
	target;
	id;
	messages: Message[] = [];
	incoming: Message[] = [];
	waiting: Waiting[] = [];
	isReady = true;
	queue: [HTMLIFrameElement, any][] = [];

	constructor(target: HTMLIFrameElement, id: string) {
		this.target = target;
		this.id = id;
	}

	_sendMessage(elem: HTMLIFrameElement, msg: any) {
		try {
			console.log('send', msg);
			elem.contentWindow!.postMessage(JSON.stringify(msg), '*');
		} catch {
		}
	}

	setReady(ready: boolean) {
		if ((this.isReady = ready) && this.queue.length)
			this.queue.forEach(msg => this._sendMessage(...msg));
		this.clearQueue();
	}

	clearQueue() {
		this.queue.splice(0);
	}

	setID(id: string) {
		this.id = id
	}

	contact(label: string, message?: any) {
		if (!this.id)
			throw new Error('Chat requires unique id to communicate between windows');
		const
			lookup = Math.random().toString(36).substring(2),
			msg = {
				source: 'hcaptcha',
				label,
				id: this.id,
				promise: 'create',
				lookup,
				contents: undefined,
			};
		if (message) {
			if (typeof message !== 'object')
				throw new Error('Message must be an object.');
			msg.contents = message;
		}
		return new Promise((resolve, reject) => {
			this.waiting.push({ label, reject, resolve, lookup });
			this._addToQueue(this.target, msg);
		});
	};

	listen(label: string, listener: Listener) {
		if (!this.id)
			throw new Error('Chat requires unique id to communicate between windows');
		for (let i = this.messages.length - 1; i >= 0; --i) {
			if (this.messages[i].label === label) {
				this.messages[i].listeners.push(listener);
				break;
			}
		}
		this.messages.push({ label, listeners: [listener] });
	}

	answer(label: string, listener: Listener) {
		if (!this.id)
			throw new Error('Chat requires unique id to communicate between windows');
		for (let i = this.incoming.length - 1; i >= 0; --i) {
			if (this.incoming[i].label === label) {
				this.incoming[i].listeners.push(listener);
				break;
			}
		}
		this.incoming.push({ label, listeners: [listener] });
	}

	send(label: string, message?: any) {
		if (!this.id)
			throw new Error('Chat requires unique id to communicate between windows');
		const toSend = {
			source: 'hcaptcha',
			label,
			id: this.id,
			contents: undefined,
		};
		if (message) {
			if (typeof message !== 'object')
				throw new Error('Message must be an object.');
			toSend.contents = message;
		}
		this._addToQueue(this.target, toSend)
	};

	check(label: string, lookup?: string) {
		const result: (Message | Waiting)[] = [];
		for (const item of [...this.messages, ...this.incoming, ...this.waiting])
			if (item.label === label && !(lookup && (<Waiting>item).lookup && (<Waiting>item).lookup !== lookup))
				result.push(item);
		return result;
	};

	respond(response: Response) {
		for (const item of [...this.messages, ...this.incoming, ...this.waiting])
			if (item.label === response.label && !(response.lookup && (<Waiting>item).lookup && (<Waiting>item).lookup !== response.lookup)) {
				const payload: any[] = [];
				if (response.error) payload.push(response.error);
				if (response.contents) payload.push(response.contents);
				switch (response.promise) {
					case 'create': {
						(<Message>item).listeners.forEach(listener => {
							payload.push(this._contactPromise(item.label, response.lookup));
							listener(...payload);
						});
						break;
					}
					case 'resolve':
					case 'reject': {
						(<Waiting>item)[response.promise](payload);
						const pos = this.waiting.findIndex(waiting => waiting.label === item.label && waiting.lookup === (<Waiting>item).lookup);
						if (~pos) this.waiting.splice(pos, 1);
						break;
					}
					default:
						(<Message>item).listeners.forEach(listener => listener(...payload));
						break;
				}
			}
	}

	destroy() {
		this.clearQueue();
		this.messages.splice(0);
		this.incoming.splice(0);
		this.waiting.splice(0);
		this.isReady = false;
		return null;
	}

	_contactPromise(label: string, lookup: string) {
		const { promise, resolve, reject } = Promise.withResolvers(), payload: any = {
			source: 'hcaptcha',
			label,
			id: this.id,
			promise: null,
			lookup,
		};
		promise.then(
			contents => {
				payload.promise = 'resolve';
				if (contents !== null) payload.contents = contents;
				this._addToQueue(this.target, payload);
			},
			error => {
				payload.promise = 'reject';
				if (error !== null) payload.error = error;
				this._addToQueue(this.target, payload);
			},
		);
		return { resolve, reject };
	};

	_addToQueue(e: HTMLIFrameElement, t: any) {
		if (this.isReady)
			this._sendMessage(e, t);
		else
			this.queue.push([e, t]);
	};
}

const chats: Chat[] = [];

export function createChat(target: HTMLIFrameElement, id: string) {
	const chat = new Chat(target, id);
	return chats.push(chat), chat;
}

export function addChat(chat: Chat) {
	chats.push(chat);
}

export function removeChat(chat: Chat) {
	const pos = chats.findIndex(c => c.id === chat.id && c.target === chat.target);
	return ~pos ? chats.splice(pos, 1)[0] : false;
}

export function handle(event: MessageEvent<string>) {
	const { data } = event, isHcaptchaEvent = typeof data === 'string' && data.includes('hcaptcha');
	if (!isHcaptchaEvent) return;
	try {
		const response = <Response>JSON.parse(data);
		console.log('recv', response);
		for (const chat of chats)
			if (chat.id === response.id)
				chat.respond(response);
	} catch {
	}
}

window.addEventListener('message', handle);
