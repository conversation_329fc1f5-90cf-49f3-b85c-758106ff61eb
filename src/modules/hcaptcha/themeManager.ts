interface Palette {
	mode: string,
	grey: Record<number, string>,
	primary: Record<string, string>,
	secondary: Record<string, string>,
	warn: Record<string, string>,
	text: Record<string, string>,
	common: Record<string, string>,
}

interface Theme {
	palette: Palette,
	[key: string]: any,
}

const
	THEME_COMMON = {
		transparent: 'transparent',
		white: '#ffffff',
		black: '#000000'
	},
	GREY = {
		100: '#fafafa',
		200: '#f5f5f5',
		300: '#E0E0E0',
		400: '#D7D7D7',
		500: '#BFBFBF',
		600: '#919191',
		700: '#555555',
		800: '#333333',
		900: '#222222',
		1e3: '#14191F'
	},
	SECONDARY = '#4DE1D2',
	PRIMARY = '#00838F',
	LIGHT: Palette = {
		mode: 'light',
		grey: GREY,
		primary: { main: PRIMARY },
		secondary: { main: SECONDARY },
		warn: {
			light: '#BF1722',
			main: '#BF1722',
			dark: '#9D1B1B',
		},
		text: {
			heading: GREY[700],
			body: GREY[700],
		},
		common: THEME_COMMON,
	},
	DARK: Palette = {
		mode: 'dark',
		grey: GREY,
		primary: { main: PRIMARY },
		secondary: { main: SECONDARY },
		warn: LIGHT.warn,
		text: {
			heading: GREY[200],
			body: GREY[200],
		},
		common: THEME_COMMON,
	};

function getStyleItem(itemKey: keyof Palette, mode: string) {
	return 'dark' === mode && itemKey in DARK ? DARK[itemKey] : LIGHT[itemKey]
}

function normalizePalette(palette?: Partial<Palette>): Palette {
	palette ??= <Palette>{};
	palette.mode ??= 'light';
	palette.primary ??= <any>getStyleItem('primary', palette.mode);
	palette.secondary ??= <any>getStyleItem('secondary', palette.mode);
	palette.warn ??= <any>getStyleItem('warn', palette.mode);
	palette.grey ??= <any>getStyleItem('grey', palette.mode);
	palette.text ??= <any>getStyleItem('text', palette.mode);
	palette.common ??= THEME_COMMON;
	return <Palette>palette;
}

export class ThemeManager {
	_themes: Record<string, Theme> = Object.create(null);
	_active = 'light';

	constructor() {
		this.add('light', {});
		this.add('dark', <Theme>{ palette: { mode: 'dark' } });
	}

	get(mode?: string) {
		if (!mode)
			return this._themes[this._active];
		const t = this._themes[mode];
		if (!t)
			throw new Error(`Cannot find theme with name: ${mode}`);
		return t
	}

	use(mode: string) {
		if (this._themes[mode])
			this._active = mode;
		else
			console.error(`Cannot find theme with name: ${mode}`);
	}

	active() {
		return this._active
	}

	add(mode: string, theme: Partial<Theme>) {
		theme.palette = normalizePalette(theme.palette);
		theme.component ??= Object.create(null);
		this._themes[mode] = <Theme>theme;
	}

	extend(mode: string, style: any) {
		if (typeof style === 'string') style = JSON.parse(style);
		return { ...JSON.parse(JSON.stringify(this.get(mode))), ...style };
	}

	merge(New: any, dest: any) {
		return { ...dest, ...New };
	}
}

export const
	THEMES = ['light', 'dark', 'contrast', 'grey-red'],
	themeManager = new ThemeManager;

themeManager.add('contrast', {});
themeManager.add('grey-red', { component: { challenge: { main: { border: '#6a6a6a' } } } });
