import { ASSET_URL } from './env';

export class InvisibleCheckbox {
	container;
	id;
	config;
	response: string | null = null;
	location = {
		tick: null,
		offset: null,
		bounding: null
	};
	$iframe;
	$textArea0;
	$textArea1;

	constructor(container: HTMLElement, id: string, config: unknown) {
		this.container = container;
		this.id = id;
		this.config = config;
		this.$iframe = document.createElement('iframe');
		(<any>this.$iframe).credentialless = true;
		this.$iframe.setAttribute('aria-hidden', 'true');
		this.$iframe.style.display = 'none';
		this.$iframe.setAttribute('data-hcaptcha-widget-id', id);
		this.$iframe.setAttribute('data-hcaptcha-response', '');
		this.$iframe.src = `${ASSET_URL}/hcaptcha.html#frame=checkbox-invisible`;
		this.$textArea0 = document.createElement('textarea');
		this.$textArea0.id = `g-recaptcha-response-${id}`;
		this.$textArea0.name = 'g-recaptcha-response';
		this.$textArea0.style.display = 'none';
		this.$textArea1 = document.createElement('textarea');
		this.$textArea1.id = `h-captcha-response-${id}`;
		this.$textArea1.name = 'h-captcha-response';
		this.$textArea1.style.display = 'none';
		this.container.append(this.$iframe, this.$textArea0, this.$textArea1);
	}

	setResponse(response: string) {
		this.response = response;
		this.$iframe.setAttribute('data-hcaptcha-response', response);
		this.$textArea0.value = response;
		this.$textArea1.value = response;
	}

	reset() { }

	clearLoading() { }

	sendTranslation(locale: string) { }

	translate() { }

	status(text?: string, a11yOnly?: boolean) { }

	tick() { }

	async getTickLocation() {
		return {
			left: 0,
			right: 0,
			top: 0,
			bottom: 0,
			width: 0,
			height: 0,
			x: 0,
			y: 0,
		}
	}

	getOffset() {
		let e: HTMLElement = this.$iframe, left = 0, top = 0;
		if (!e.offsetParent) e = e.parentElement!;
		for (; e; e = <HTMLElement>e.offsetParent!) {
			left += e.offsetLeft;
			top += e.offsetTop;
		}
		return { top, left };
	}

	getBounding() {
		return this.$iframe.getBoundingClientRect()
	}

	destroy() {
		this.$iframe.remove();
		this.$textArea0.remove();
		this.$textArea1.remove();
	}
}
