export const
	INVALID_CAPTCHA_ID = 'invalid-captcha-id',
	MISSING_CAPTCHA = 'missing-captcha',
	MISSING_SITEKEY = 'missing-sitekey';

export function errorInvalidID(id: string) {
	return new Error(`Invalid hCaptcha id: ${id}`, { cause: INVALID_CAPTCHA_ID });
}

export function errorMissingCaptcha() {
	return new Error('No hCaptcha exists.', { cause: MISSING_CAPTCHA });
}

export function errorMissingSitekey() {
	return new Error('Missing sitekey - https://docs.hcaptcha.com/configuration#javascript-api', { cause: MISSING_SITEKEY });
}
