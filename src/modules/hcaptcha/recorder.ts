import { <PERSON>rowser } from './browserInfo';
import { pickDevice, pickKey, pickMouse, pickPointer, pickTouch } from './event';
import { TimeBuffer } from './timeBuffer';

class Recorder {
	_manifest: Record<string, any> = {};
	state = {
		timeBuffers: <Record<string, TimeBuffer<number[]>>>{},
		loadTime: Date.now(),
		recording: false,
		initRecord: false,
		record: {
			mouse: true,
			touch: true,
			keys: false,
			motion: false
		}
	};

	constructor() {
		this._recordEvent = this._recordEvent.bind(this);
	}

	record(recordMouse?: boolean, recordTouch?: boolean, recordKeys?: boolean, recordMotion?: boolean) {
		this._manifest.st = Date.now();
		this.state.record.mouse = recordMouse == null ? this.state.record.mouse : recordMouse;
		this.state.record.touch = recordTouch == null ? this.state.record.touch : recordTouch;
		this.state.record.keys = recordKeys == null ? this.state.record.keys : recordKeys;
		this.state.record.motion = recordMotion == null ? this.state.record.motion : recordMotion;
		if (!this.state.initRecord) {
			if (this.state.record.mouse) {
				document.body.addEventListener('mousedown', pickMouse('mousedown', this._recordEvent), true);
				document.body.addEventListener('mousemove', pickMouse('mousemove', this._recordEvent), true);
				document.body.addEventListener('mouseup', pickMouse('mouseup', this._recordEvent), true);
				document.body.addEventListener('pointermove', pickPointer('pointermove', this._recordEvent), true);
			}
			if (this.state.record.keys) {
				document.body.addEventListener('keyup', pickKey('keyup', this._recordEvent), true);
				document.body.addEventListener('keydown', pickKey('keydown', this._recordEvent), true);
			}
			if (this.state.record.touch && Browser.hasEvent('touchstart', document.body)) {
				document.body.addEventListener('touchstart', pickTouch('touchstart', this._recordEvent), true);
				document.body.addEventListener('touchmove', pickTouch('touchmove', this._recordEvent), true);
				document.body.addEventListener('touchend', pickTouch('touchend', this._recordEvent), true);
			}
			if (this.state.record.motion && Browser.hasEvent('devicemotion', window)) {
				document.body.addEventListener('devicemotion', <any>pickDevice('devicemotion', this._recordEvent), true);
			}
			this.state.initRecord = true;
		}
		this.state.recording = true;
	}

	stop() {
		this.state.recording = false;
	}

	time() {
		return this.state.loadTime;
	};

	getData() {
		for (const key in this.state.timeBuffers) {
			this._manifest[key] = this.state.timeBuffers[key].getData();
			this._manifest[`${key}-mp`] = this.state.timeBuffers[key].getMeanPeriod();
		}
		return this._manifest
	};

	setData(key: string, value: any) {
		this._manifest[key] = value;
	};

	resetData() {
		this._manifest = {};
		this.state.timeBuffers = {};
	};

	circBuffPush(key: string, value: number[]) {
		this._recordEvent(key, value)
	};

	_recordEvent(key: string, value: number[]) {
		if (this.state.recording !== false)
			try {
				const last = value.at(-1);
				this.state.timeBuffers[key] ??= new TimeBuffer(16, 15e3);
				this.state.timeBuffers[key].push(last!, value);
			} catch { }
	}
};

export const recorder = new Recorder();
