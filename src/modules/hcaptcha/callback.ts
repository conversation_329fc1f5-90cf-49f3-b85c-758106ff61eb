export function runCallback(name: string | Function, ...args: unknown[]) {
	if (typeof name === 'string') {
		if ((<any>globalThis)[name]) {
			if (typeof (<any>globalThis)[name] === 'function') {
				return (<any>globalThis)[name](...args);
			} else {
				console.log(`[hCaptcha] Callback '${name}' is not a function.`);
			}
		} else {
			console.log(`[hCaptcha] Callback '${name}' is not defined.`);
		}
	} else if (typeof name === 'function') {
		return name(...args);
	} else {
		console.log(`[hCaptcha] Invalid callback '${name}'.`);
	}
}
