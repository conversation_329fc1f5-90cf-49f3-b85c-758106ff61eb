import { remove as drop, getR<PERSON><PERSON><PERSON><PERSON> } from './api';
import { <PERSON><PERSON><PERSON> } from './browserInfo';
import { runCallback } from './callback';
import { Challenge, type ChallengeConfig } from './challenge';
import { Checkbox } from './checkbox';
import { errorMissingSitekey } from './errors';
import { CHALLENGE_CLOSED, CHALLENGE_ESCAPED, CHALLENGE_EXPIRED, CHALLENGE_PASSED } from './event';
import { InvisibleCheckbox } from './invisibleCheckbox';
import { recorder } from './recorder';
import { System } from './systemInfo';
import { THEMES, themeManager } from './themeManager';
import { loadLocale, resolveLocale, setLocale } from './translator';

const
	INVALID_DATA = 'invalid-data',
	BUNDLE_ERROR = 'bundle-error',
	RATE_LIMITED = 'rate-limited',
	NETWORK_ERROR = 'network-error',
	CHALLENGE_ERROR = 'challenge-error',
	INCOMPLETE_ANSWER = 'incomplete-answer';

export interface NodeConfig extends ChallengeConfig {
	sitekey: string;
	theme: string | object;
	hl: string;
	size?: string;
	themeConfig?: any;
}

export class Node {
	id;
	config;
	visible = false;
	overflow = {
		override: false,
		cssUsed: true,
		value: null,
		scroll: 0,
	};
	onError = null;
	onPass = null;
	onExpire = null;
	onChalExpire = null;
	onOpen = null;
	onClose = null;
	_ready = false;
	_active = false;
	_listeners: (() => void)[] = [];
	_state = {
		escaped: false,
		passed: false,
		expiredChallenge: false,
		expiredResponse: false,
	};
	_origData = null;
	_langSet = false;
	_promise: { resolve: (x: any) => void, reject: (x: any) => void } = null!;
	_responseTimer = 0;
	challenge;
	checkbox;

	constructor(container: HTMLElement, id: string, config: NodeConfig) {
		if (!config.sitekey)
			throw errorMissingSitekey();
		this.id = id;
		this.config = config;
		if (typeof config.theme === 'string' && THEMES.includes(config.theme)) themeManager.use(config.theme);
		this.challenge = new Challenge(id, config);
		if (config.size === 'invisible') {
			this.checkbox = new InvisibleCheckbox(container, id, config);
		} else {
			this.checkbox = new Checkbox(container, id, config);
		}

		this.initChallenge = this.initChallenge.bind(this);
	}

	_resetTimer() {
		if (this._responseTimer)
			clearTimeout(this._responseTimer);
		this._responseTimer = 0;
	}

	initChallenge(data?: any) {
		this._origData = data ??= {};
		const
			a11yChallenge = data.a11yChallenge ?? false,
			manifest = this.getGetCaptchaManifest(),
			width = Browser.width(),
			height = Browser.height(),
			charity = data.charity ?? null,
			link = data.link ?? null,
			action = data.action ?? '',
			rqdata = data.rqdata ?? null,
			wdata = Object.keys(window).sort().join(','),
			errors = data.errors ?? [];
		this._active = true;
		this._resetTimer();
		this._resetState();
		this.checkbox.setResponse('');
		this.challenge.setup({
			a11yChallenge,
			manifest,
			width,
			height,
			charity,
			link,
			action,
			rqdata,
			wdata,
			errors,
		});
	}

	getGetCaptchaManifest() {
		return {
			st: Date.now(),
			...(<any>this._origData)?.manifest,
			v: 1,
			topLevel: recorder.getData(),
			session: getSessions(),
			widgetList: getCaptchaIdList(),
			widgetId: this.id,
			href: window.location.href,
			prev: JSON.parse(JSON.stringify(this._state)),
		};
	}

	displayChallenge(data: { width: number, height: number, mobile: boolean }) {
		if (this._active) {
			this.visible = true;
			const
				checkbox = this.checkbox,
				challenge = this.challenge,
				height = Browser.height(),
				overflowY = window.getComputedStyle(document.body).getPropertyValue('overflow-y');
			if (this.overflow.override = overflowY === 'hidden') {
				this.overflow.cssUsed = document.body.style.overflow === '' && document.body.style.overflowY === '';
				if (!this.overflow.cssUsed)
					this.overflow.value = <any>'hidden';
				this.overflow.scroll = Browser.scrollY();
				document.body.style.overflowY = 'auto';
			}
			checkbox.status();
			return checkbox.getTickLocation().then(location => {
				if (this._active) {
					challenge.size(data.width, data.height, data.mobile);
					challenge.show();
					checkbox.clearLoading();
					checkbox.location.bounding = <any>checkbox.getBounding();
					checkbox.location.tick = <any>location;
					checkbox.location.offset = <any>checkbox.getOffset();
					challenge.position(checkbox.location);
					challenge.focus();
					if (challenge.height > window.document.documentElement.clientHeight) {
						(window.document.scrollingElement ?? document.documentElement).scrollTop = Math.abs(challenge.height - height) + challenge.top;
					}
				}
			});
		}
	}

	resize(width: number, height: number, updated?: boolean) {
		const checkbox = this.checkbox, challenge = this.challenge;
		challenge.setDimensions(width, height)?.then(
			(dimension: any) => {
				if (dimension) challenge.size(dimension.width, dimension.height, dimension.mobile);
				checkbox.location.bounding = <any>checkbox.getBounding();
				checkbox.location.offset = <any>checkbox.getOffset();
				if (!System.mobile || updated) challenge.position(checkbox.location);
			},
			error => this.closeChallenge({
				event: CHALLENGE_ERROR,
				message: 'Captcha resize caused error.',
				error,
			})
		);
	}

	position() {
		const checkbox = this.checkbox, challenge = this.challenge;
		if (!System.mobile) {
			checkbox.location.bounding = <any>checkbox.getBounding();
			challenge.position(checkbox.location);
		}
	}

	reset() {
		try {
			this.checkbox.reset();
			this.checkbox.setResponse('');
			this._resetTimer();
			this._resetState();
		} catch { }
	}

	_resetState() {
		for (const key in this._state)
			(<any>this._state)[key] = false;
	};

	closeChallenge(data: any) {
		this.visible = false;
		this._active = false;
		const checkbox = this.checkbox, challenge = this.challenge;
		if (this.overflow.override) {
			(window.document.scrollingElement || document.documentElement).scrollTop = this.overflow.scroll;
			this.overflow.override = false;
			this.overflow.scroll = 0;
			document.body.style.overflowY = <any>(this.overflow.cssUsed ? null : this.overflow.value);
		}
		const response = data.response ?? '';
		checkbox.setResponse(response);
		challenge.close(data.event);
		checkbox.$iframe.focus();
		switch (data.event) {
			case CHALLENGE_ESCAPED:
				this._state.escaped = true;
				checkbox.reset();
				if (this.onClose) runCallback(this.onClose);
				if (this._promise) this._promise.reject(CHALLENGE_CLOSED);
				break;
			case CHALLENGE_EXPIRED:
				this._state.expiredChallenge = true;
				checkbox.reset();
				checkbox.status('hCaptcha window closed due to timeout.', true);
				this.onChalExpire && runCallback(this.onChalExpire);
				if (this._promise) this._promise.reject(CHALLENGE_EXPIRED);
				break;
			case CHALLENGE_ERROR:
			case BUNDLE_ERROR:
			case NETWORK_ERROR:
				let msg = data.event;
				checkbox.reset();
				switch (data.event) {
					case NETWORK_ERROR: {
						checkbox.status(data.message);
						if (data.status === 429)
							msg = RATE_LIMITED;
						else if (data.message === INVALID_DATA)
							msg = INVALID_DATA;
						else if (data.message === 'client-fail')
							msg = CHALLENGE_ERROR;
						break;
					}
					case BUNDLE_ERROR: {
						msg = CHALLENGE_ERROR;
						break;
					}
					case CHALLENGE_ERROR: {
						if (data.message === 'Answers are incomplete')
							msg = INCOMPLETE_ANSWER;
						break;
					}
				}
				if (this.onError) runCallback(this.onError, msg);
				if (this._promise) this._promise.reject(msg);
				break;
			case CHALLENGE_PASSED:
				this._state.passed = true;
				checkbox.tick();
				if (this.onPass) runCallback(this.onPass, response);
				if (this._promise) {
					this._promise.resolve({ response, key: getRespKey(this.id) });
				}
				if (typeof data.expiration === 'number') {
					this._resetTimer();
					this._responseTimer = setTimeout(() => {
						try {
							if (checkbox.$iframe) {
								if (checkbox.$iframe.contentWindow) {
									checkbox.reset();
									checkbox.setResponse('');
									checkbox.status('hCaptcha security token has expired. Please complete the challenge again.', true);
								} else
									drop(this.id);
							}
						} catch { }
						if (this.onExpire) runCallback(this.onExpire);
						this._responseTimer = 0;
						this._state.expiredResponse = true;
					}, data.expiration * 1000);
				}
		}
		this._promise = null!;
	};

	updateTranslation(locale: string) {
		this.config.hl = locale;
		this._langSet = true;
		if (this.checkbox) this.checkbox.sendTranslation(locale);
		if (this.challenge) this.challenge.sendTranslation(locale);
	};

	isLangSet() {
		return this._langSet;
	};

	isReady() {
		return this._ready;
	};

	setReady(ready: boolean) {
		if (this._ready = ready) {
			for (; this._listeners.length; this._listeners.pop()!());
		}
	};

	setPromise(promise: { resolve: (x: any) => void, reject: (x: any) => void }) {
		this._promise = promise;
	};

	onReady(callback: (...args: unknown[]) => void, ...args: unknown[]) {
		const curried = () => callback(...args);
		this._ready ? curried() : this._listeners.push(curried);
	};

	destroy() {
		this._resetTimer();
		if (this.overflow.override) {
			(window.document.scrollingElement ?? document.getElementsByTagName('html')[0]).scrollTop = this.overflow.scroll;
			this.overflow.override = false;
			this.overflow.scroll = 0;
			document.body.style.overflowY = <any>(this.overflow.cssUsed ? null : this.overflow.value);
		}
		this.challenge.destroy();
		this.checkbox.destroy();
		this.challenge = null!;
		this.checkbox = null!;
	};

	async setSiteConfig(config: any) {
		if ('ok' in config) {
			const n = config.ok.features ?? {};
			if (this.config.themeConfig && n.custom_theme) {
				const r = `custom-${this.id}`;
				themeManager.add(r, themeManager.extend(themeManager.active(), this.config.themeConfig));
				themeManager.use(r);
				this.challenge.style();
			}
		}
		if (this.config.size === 'invisible') {
			if ('err' in config) {
				console.error(`[hCaptcha] ${config.err.message}`);
			}
		} else {
			await (<Checkbox>this.checkbox).ready;
			(<Checkbox>this.checkbox).chat.send('site-setup', config);
			return new Promise(fulfill => (<Checkbox>this.checkbox).chat.listen('checkbox-loaded', fulfill));
		}
	}
}

export function updateTranslation(node: Node | null, locale: string) {
	if (node)
		try {
			node.updateTranslation(locale)
		} catch { }
}

export function updateLocale({ locale }: { locale: string }, loaded: boolean, node: Node) {
	if (locale) {
		const lang = resolveLocale(locale);
		loadLocale(lang).then(() => {
			if (loaded) {
				updateTranslation(node, lang);
			} else {
				setLocale(locale);
				each(node => updateTranslation(node, lang));
			}
		}, () => { })
	}
}

const
	_nodes: Node[] = [],
	sessions: [string, string][] = [];

export function add(node: Node) {
	_nodes.push(node);
}

export function remove(node: Node) {
	const pos = _nodes.findIndex(_node => _node.id === node.id);
	return ~pos ? _nodes.splice(pos, 1)[0] : false;
}

export function each(callback: (node: Node, index: number, nodes: Node[]) => void) {
	_nodes.forEach(callback);
}

export function isValidId(id: string) {
	return _nodes.some(node => node.id === id);
}

export function getByIndex(idx: number) {
	return _nodes[idx];
}

export function getById(id: string) {
	return _nodes.find(node => node.id === id);
}

export function getCaptchaIdList() {
	return _nodes.map(node => node.id);
}

export function pushSession(e: any, t: any) {
	sessions.push([e, t]);
	if (sessions.length > 10) sessions.splice(0, sessions.length - 10);
}

export function getSessions() {
	return sessions;
}
