export class TimeBuffer<T> {
	_period;
	_interval;
	_date: number[] = [];
	_data: T[] = [];
	_prevTimestamp = 0;
	_meanPeriod = 0;
	_meanCounter = 0;

	constructor(period: number, interval: number) {
		this._period = period;
		this._interval = interval;
	}

	getMeanPeriod() {
		return this._meanPeriod;
	}

	getData() {
		this._cleanStaleData();
		return this._data;
	}

	getSize() {
		this._cleanStaleData();
		return this._data.length;
	}

	getCapacity() {
		return this._period === 0 ? this._interval : Math.ceil(this._interval / this._period);
	}

	push(date: number, data: T) {
		this._cleanStaleData();
		const isDateEmpty = this._date.length === 0;
		if (date - (this._date.at(-1) ?? 0) >= this._period) {
			this._date.push(date);
			this._data.push(data);
		}
		if (!isDateEmpty) {
			const diff = date - this._prevTimestamp;
			this._meanPeriod = (this._meanPeriod * this._meanCounter + diff) / (this._meanCounter + 1);
			this._meanCounter++;
		}
		this._prevTimestamp = date;
	}

	_cleanStaleData() {
		const now = Date.now();
		for (let i = this._date.length - 1; i >= 0; --i)
			if (now - this._date[i] >= this._interval) {
				this._date.splice(0, i + 1);
				this._data.splice(0, i + 1);
				break;
			}
	}
}
