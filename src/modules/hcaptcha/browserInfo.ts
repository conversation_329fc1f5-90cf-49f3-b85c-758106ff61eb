import { match, userAgent, type Candidate } from './commonInfo';

const USER_AGENTS: Candidate[] = [
	{
		family: 'UC Browser',
		patterns: [
			'(UC? ?Browser|UCWEB|U3)[ /]?(\\d+)\\.(\\d+)\\.(\\d+)',
		],
	},
	{
		family: 'Opera',
		name_replace: 'Opera Mobile',
		patterns: [
			'(Opera)/.+Opera Mobi.+Version/(\\d+)\\.(\\d+)',
			'(Opera)/(\\d+)\\.(\\d+).+Opera Mobi',
			'Opera Mobi.+(Opera)(?:/|\\s+)(\\d+)\\.(\\d+)',
			'Opera Mobi',
			'(?:Mobile Safari).*(OPR)/(\\d+)\\.(\\d+)\\.(\\d+)',
		],
	},
	{
		family: 'Opera',
		name_replace: 'Opera Mini',
		patterns: [
			'(Opera Mini)(?:/att|)/?(\\d+|)(?:\\.(\\d+)|)(?:\\.(\\d+)|)',
			'(OPiOS)/(\\d+).(\\d+).(\\d+)',
		],
	},
	{
		family: 'Opera',
		name_replace: 'Opera Neon',
		patterns: [
			'Chrome/.+( MMS)/(\\d+).(\\d+).(\\d+)',
		],
	},
	{
		name_replace: 'Opera',
		patterns: [
			'(Opera)/9.80.*Version/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)',
			'(?:Chrome).*(OPR)/(\\d+)\\.(\\d+)\\.(\\d+)',
		],
	},
	{
		family: 'Firefox',
		name_replace: 'Firefox Mobile',
		patterns: [
			'(Fennec)/(\\d+)\\.(\\d+)\\.?([ab]?\\d+[a-z]*)',
			'(Fennec)/(\\d+)\\.(\\d+)(pre)',
			'(Fennec)/(\\d+)\\.(\\d+)',
			'(?:Mobile|Tablet);.*(Firefox)/(\\d+)\\.(\\d+)',
			'(FxiOS)/(\\d+)\\.(\\d+)(\\.(\\d+)|)(\\.(\\d+)|)',
		],
	},
	{
		name_replace: 'Coc Coc',
		patterns: [
			'(coc_coc_browser)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)',
		],
	},
	{
		family: 'QQ',
		name_replace: 'QQ Mini',
		patterns: [
			'(MQQBrowser/Mini)(?:(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)|)',
		],
	},
	{
		family: 'QQ',
		name_replace: 'QQ Mobile',
		patterns: [
			'(MQQBrowser)(?:/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)|)',
		],
	},
	{
		name_replace: 'QQ',
		patterns: [
			'(QQBrowser)(?:/(\\d+)(?:\\.(\\d+)\\.(\\d+)(?:\\.(\\d+)|)|)|)',
		],
	},
	{
		family: 'Edge',
		name_replace: 'Edge Mobile',
		patterns: [
			'Windows Phone .*(Edge)/(\\d+)\\.(\\d+)',
			'(EdgiOS|EdgA)/(\\d+)\\.(\\d+).(\\d+).(\\d+)',
		],
	},
	{
		name_replace: 'Edge',
		patterns: [
			'(Edge|Edg)/(\\d+)(?:\\.(\\d+)|)',
		],
	},
	{
		patterns: [
			'(Puffin)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)',
		],
	},
	{
		family: 'Chrome',
		name_replace: 'Chrome Mobile',
		patterns: [
			'Version/.+(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)',
			'; wv\\).+(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)',
			'(CriOS)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)',
			'(CrMo)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)',
			'(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+) Mobile(?:[ /]|$)',
			' Mobile .*(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)',
		],
	},
	{
		family: 'Yandex',
		name_replace: 'Yandex Mobile',
		patterns: [
			'(YaBrowser)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+).*Mobile',
		],
	},
	{
		name_replace: 'Yandex',
		patterns: [
			'(YaBrowser)/(\\d+)\\.(\\d+)\\.(\\d+)',
		],
	},
	{
		patterns: [
			'(Vivaldi)/(\\d+)\\.(\\d+)',
			'(Vivaldi)/(\\d+)\\.(\\d+)\\.(\\d+)',
		],
	},
	{
		name_replace: 'Brave',
		patterns: [
			'(brave)/(\\d+)\\.(\\d+)\\.(\\d+) Chrome',
		],
	},
	{
		family: 'Chrome',
		patterns: [
			'(Chromium|Chrome)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)',
		],
	},
	{
		name_replace: 'Internet Explorer Mobile',
		patterns: [
			'(IEMobile)[ /](\\d+)\\.(\\d+)',
		],
	},
	{
		family: 'Safari',
		name_replace: 'Safari Mobile',
		patterns: [
			'(iPod|iPhone|iPad).+Version/(d+).(d+)(?:.(d+)|).*[ +]Safari',
			'(iPod|iPod touch|iPhone|iPad);.*CPU.*OS[ +](\\d+)_(\\d+)(?:_(\\d+)|).* AppleNews\\/\\d+\\.\\d+\\.\\d+?',
			'(iPod|iPhone|iPad).+Version/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)',
			'(iPod|iPod touch|iPhone|iPad);.*CPU.*OS[ +](\\d+)_(\\d+)(?:_(\\d+)|).*Mobile.*[ +]Safari',
			'(iPod|iPod touch|iPhone|iPad);.*CPU.*OS[ +](\\d+)_(\\d+)(?:_(\\d+)|).*Mobile',
			'(iPod|iPod touch|iPhone|iPad).* Safari',
			'(iPod|iPod touch|iPhone|iPad)',
		],
	},
	{
		name_replace: 'Safari',
		patterns: [
			'(Version)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|).*Safari/',
		],
	},
	{
		name_replace: 'Internet Explorer',
		patterns: [
			'(Trident)/(7|8).(0)',
		],
		major_replace: '11',
	},
	{
		name_replace: 'Internet Explorer',
		patterns: [
			'(Trident)/(6)\\.(0)',
		],
		major_replace: '10',
	},
	{
		name_replace: 'Internet Explorer',
		patterns: [
			'(Trident)/(5)\\.(0)',
		],
		major_replace: '9',
	},
	{
		name_replace: 'Internet Explorer',
		patterns: [
			'(Trident)/(4)\\.(0)',
		],
		major_replace: '8',
	},
	{
		family: 'Firefox',
		patterns: [
			'(Firefox)/(\\d+)\\.(\\d+)\\.(\\d+)',
			'(Firefox)/(\\d+)\\.(\\d+)(pre|[ab]\\d+[a-z]*|)',
		],
	},
];

export class BrowserInfo {
	agent = userAgent.toLowerCase();
	language = window.navigator.language;
	type;
	version;

	constructor() {
		const version = match(userAgent, USER_AGENTS);
		this.type = version.family === 'Edge' ? 'edge' :
			version.family === 'Internet Explorer' ? 'ie' :
				version.family === 'Chrome' ? 'chrome' :
					version.family === 'Safari' ? 'safari' :
						version.family === 'Firefox' ? 'firefox' :
							version.family.toLowerCase();
		this.version = Number(`${version.major}.${version.minor}`);
		if (!Number.isFinite(this.version))
			this.version = 0;
	}

	width() {
		return window.innerWidth && window.document.documentElement.clientWidth ?
			Math.min(window.innerWidth, document.documentElement.clientWidth) :
			window.innerWidth ?? window.document.documentElement.clientWidth ?? document.body.clientWidth;
	}

	height() {
		return window.innerHeight ?? window.document.documentElement.clientHeight ?? document.body.clientHeight;
	}

	scrollX() {
		return window.pageXOffset ?? document.documentElement.scrollLeft;
	}

	scrollY() {
		return window.pageYOffset ?? document.documentElement.scrollTop;
	}

	hasEvent(eventName: string, element?: EventTarget) {
		return `on${eventName}` in (element ?? document.createElement('div'));
	}

	getScreenDimensions(): Omit<Screen, 'orientation'> {
		const dim: any = {};
		for (const key in window.screen)
			dim[key] = (<any>window.screen)[key];
		delete dim.orientation;
		return dim;
	}

	getWindowDimensions(): [number, number] {
		return [this.width(), this.height()]
	}

	interrogateNavigator() {
		const nav: Record<string, unknown> & { plugins: string[] } = { plugins: [] };
		for (const t in window.navigator)
			if ('webkitPersistentStorage' !== t)
				try {
					nav[t] = (<any>window.navigator)[t]
				} catch { }
		delete nav.mimeTypes;
		nav.plugins = [];
		if (window.navigator.plugins)
			for (const n in window.navigator.plugins)
				nav.plugins[n] = window.navigator.plugins[n].filename;
		return nav;
	}

	supportsPST() {
		return typeof (<any>document).hasPrivateToken === 'function';
	}
}

export const Browser = new BrowserInfo();
