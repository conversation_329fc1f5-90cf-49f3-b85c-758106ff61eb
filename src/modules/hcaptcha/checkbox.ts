import { Browser } from './browserInfo';
import { createChat, removeChat } from './chat';
import { ASSET_URL } from './env';
import { getTable, translate } from './translator';
import { makeQueryString } from './util';

export interface CheckboxConfig {
	size?: string;
	tabindex?: number;
}

export class Checkbox {
	container;
	id;
	config;
	response: string | null = null;
	location = {
		tick: null,
		offset: null,
		bounding: null
	};
	_ticked = true;
	$iframe;
	chat;
	ready;
	_timeoutFailedToInitialize
	$textArea0;
	$textArea1

	constructor(container: HTMLElement, id: string, config: CheckboxConfig) {
		this.container = container;
		this.id = id;
		this.config = config;
		this.$iframe = document.createElement('iframe');
		this.$iframe.src = `${ASSET_URL}/hcaptcha.html#frame=checkbox&id=${id}&host=${window.location.hostname}&${makeQueryString(config)}`;
		(<any>this.$iframe).credentialless = true;
		(<any>this.$iframe).tabIndex = config.tabindex ?? 0;
		(<any>this.$iframe).frameBorder = '0';
		(<any>this.$iframe).scrolling = 'no';
		this.chat = createChat(this.$iframe, id);
		this.chat.setReady(false);
		this._timeoutFailedToInitialize = setTimeout(() => { }, 60_000);
		if (Browser.supportsPST())
			this.$iframe.allow = "private-state-token-issuance 'src'; private-state-token-redemption 'src'";
		this.translate();
		if (this.config.size === 'invisible')
			this.$iframe.setAttribute('aria-hidden', 'true');
		this.$iframe.setAttribute('data-hcaptcha-widget-id', id);
		this.$iframe.setAttribute('data-hcaptcha-response', '');
		this.$textArea0 = document.createElement('textarea');
		this.$textArea0.id = `g-recaptcha-response-${id}`;
		this.$textArea0.name = 'g-recaptcha-response';
		this.$textArea0.style.display = 'none';
		this.$textArea1 = document.createElement('textarea');
		this.$textArea1.id = `h-captcha-response-${id}`;
		this.$textArea1.name = 'h-captcha-response';
		this.$textArea1.style.display = 'none';
		this.container.append(this.$iframe, this.$textArea0, this.$textArea1);
		this.ready = new Promise(fulfill => this.chat.listen('checkbox-ready', fulfill))
			.then(() => {
				if (this._timeoutFailedToInitialize) {
					clearTimeout(this._timeoutFailedToInitialize);
					this._timeoutFailedToInitialize = 0;
				}
				if (this.chat) {
					this.chat.setReady(true);
				}
			});
		this.style();
	}

	setResponse(response: string) {
		this.response = response;
		this.$iframe.setAttribute('data-hcaptcha-response', response);
		this.$textArea0.value = response;
		this.$textArea1.value = response;
	}

	style() {
		this.$iframe.style.pointerEvents = 'auto';
		this.$iframe.style.backgroundColor = 'transparent';
		switch (this.config.size) {
			case 'compact':
				this.$iframe.style.width = '164px';
				this.$iframe.style.height = '144px';
				break;
			case 'invisible':
				this.$iframe.style.display = 'none';
				break;
			default:
				this.$iframe.style.width = '303px';
				this.$iframe.style.height = '78px';
				this.$iframe.style.overflow = 'hidden';
				break;
		}
	}

	reset() {
		this._ticked = false;
		if (this.$iframe.contentWindow && this.chat)
			this.chat.send('checkbox-reset');
	}

	clearLoading() {
		if (this.chat)
			this.chat.send('checkbox-clear');
	}

	sendTranslation(locale: string) {
		if (this.chat)
			this.chat.send('checkbox-translate', { locale, table: getTable(locale) ?? {} });
		this.translate();
	}

	translate() {
		this.$iframe.title = translate('Widget containing checkbox for hCaptcha security challenge');
	}

	status(text?: string, a11yOnly?: boolean) {
		if (this.$iframe.contentWindow && this.chat)
			this.chat.send('checkbox-status', {
				text: text ?? null,
				a11yOnly: a11yOnly ?? false
			})
	}

	tick() {
		this._ticked = true;
		if (this.chat) this.chat.send('checkbox-tick')
	}

	getTickLocation() {
		return this.chat.contact('checkbox-location')
	}

	getOffset() {
		let e: HTMLElement = this.$iframe, left = 0, top = 0;
		if (!e.offsetParent) e = e.parentElement!;
		for (; e; e = <HTMLElement>e.offsetParent!) {
			left += e.offsetLeft;
			top += e.offsetTop;
		}
		return { top, left };
	}

	getBounding() {
		return this.$iframe.getBoundingClientRect()
	}

	destroy() {
		if (this._timeoutFailedToInitialize) {
			clearTimeout(this._timeoutFailedToInitialize);
			this._timeoutFailedToInitialize = 0;
		}
		if (this._ticked) this.reset();
		removeChat(this.chat);
		this.chat.destroy();
		this.$iframe.remove();
		this.$textArea0.remove();
		this.$textArea1.remove();
	}
}
