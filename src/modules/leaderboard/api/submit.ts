import type { LeaderBoardEntry } from '../../../models/leaderboard/leaderboard';
import type { SubmitData } from '../../../models/leaderboard/submitdata';
import { POST } from '../../../util/web';
import { getBackendUrl } from './url';

interface SubmitResponse {
	code: number;
	data?: {
		leaderboard: LeaderBoardEntry[];
	}
}

export async function submit(data: SubmitData): Promise<LeaderBoardEntry[]> {
	const url = new URL('/submit', getBackendUrl());
	const response: SubmitResponse = await POST(url, data);
	if (response.code === 0 && Array.isArray(response.data?.leaderboard)) {
		// eslint-disable-next-line
		return response.data?.leaderboard!;
	}
	switch (response.code) {
		case -1: throw new TypeError(`ID ${data.user} 不合法`);
		case -2: throw new TypeError('头像不合法或太大了');
		case -3: throw new TypeError('提交数据格式错误');
		default: throw new Error(`未知错误，错误码 ${response.code}`);
	}
}
