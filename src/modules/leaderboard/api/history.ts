import type { HistoryData } from '../../../models/leaderboard/history';
import { GET } from '../../../util/web';
import { getBackendUrl } from './url';

interface HistoryResponse {
	code: number;
	data?: HistoryData[];
}

export async function getHistory(user: string): Promise<HistoryData[]> {
	const url = new URL(`/history/${user}`, getBackendUrl());
	const response: HistoryResponse = await GET(url);
	if (response.code === 0 && Array.isArray(response.data)) return response.data;
	if (response.code === -1) throw new ReferenceError(`ID 为 ${user} 的用户不存在`);
	throw new Error(`未知错误，错误码 ${response.code}`);
}
