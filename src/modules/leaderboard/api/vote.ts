import type { LeaderBoardEntry } from '../../../models/leaderboard/leaderboard';
import { POST } from '../../../util/web';
import { getBackendUrl } from './url';

interface VoteResponse {
	code: number;
	data?: {
		leaderboard: LeaderBoardEntry[];
	}
}

export async function vote(user: string): Promise<LeaderBoardEntry[]> {
	const url = new URL('/vote', getBackendUrl());
	const response: VoteResponse = await POST(url, { user });
	if (response.code === 0 && Array.isArray(response.data?.leaderboard)) {
		// eslint-disable-next-line
		return response.data?.leaderboard!;
	}
	switch (response.code) {
		case -1: throw new TypeError(`ID ${user} 不合法`);
		case -2: throw new ReferenceError(`ID 为 ${user} 的用户不存在`);
		default: throw new Error(`未知错误，错误码 ${response.code}`);
	}
}
