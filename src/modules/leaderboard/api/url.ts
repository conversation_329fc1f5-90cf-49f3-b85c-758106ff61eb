import { ConfigCenter, ConfigPath } from '../../../util/config';

const
	defaultBackendUrl = new URL('https://s.back.sast2022.lmd.red/'),
	backendUrlPath = new ConfigPath(['sast2022', 'leaderBoard', 'backendUrl']);

let backendUrl: URL | undefined;

export function getBackendUrl() {
	return backendUrl ??= (() => {
		try {
			return new URL(<string>backendUrlPath.get(ConfigCenter.getState().config));
		} catch {
			return defaultBackendUrl;
		}
	})();
}
