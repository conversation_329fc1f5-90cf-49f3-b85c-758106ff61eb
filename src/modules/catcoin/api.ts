import type { Emitter as EmitterType, EventsMap } from '@socket.io/component-emitter';
import type { Manager } from 'socket.io-client';
import type { StoreApi, UseBoundStore } from 'zustand';

import { Emitter, socket } from '../../util/socket';
import { Block } from './block';
import { getURL } from './url';

export class Connection {
	url?: URL;
	conn?: Manager;
	api?: EmitterType<EventsMap, EventsMap>;
	height?: number;
	blocksCenter: UseBoundStore<StoreApi<Block[]>>;

	constructor(blocksCenter: UseBoundStore<StoreApi<Block[]>>) {
		this.blocksCenter = blocksCenter;
	}

	assign(urlStr: string) {
		const url = getURL(urlStr);
		if (this.url?.href === url.href) return;
		this.close();
		this.url = url;
		this.conn = socket(url);
		this.api = new Emitter();
		this.height = -1;

		this.conn.on('data', (data: string | ArrayBuffer) => {
			if (typeof data === 'string') {
				let json, type;
				try {
					json = JSON.parse(data as string);
					type = json.type;
					delete json.type;
				} catch {
					return;
				}
				this.api?.emit(type, json);
			} else {
				this.api?.emit('block', data);
			}
		});
		this.api.on('lengthChange', (data: { height: number }) => {
			const height = data.height + 1;
			this.blocksCenter.setState(blocks => {
				const New = blocks.slice();
				New.length = height;
				return New;
			}, true);
			this.conn?.engine.send(JSON.stringify(data));
		});
		this.api.on('block', (data: ArrayBuffer) => {
			const height = Number(new DataView(data).getBigUint64(0, false));
			const hash = new Uint8Array(data, 8, 32);
			const raw = new Uint8Array(data, 40);
			const block = new Block(height, hash, raw);
			const currentCenter = this.blocksCenter.getState();
			const prev = currentCenter[height - 1];

			this.blocksCenter.setState(blocks => {
				if (height >= blocks.length) return blocks;
				const New = blocks.slice();
				New[height] = block;
				return New;
			}, true);

			if (height <= 0) return;

			if (!(prev && block.prevHash.equals(prev.hash)) && currentCenter.length - height <= 100) {
				this.conn?.engine.send(JSON.stringify({ height: height - 1 }));
			}
		});
	}

	close() {
		this.url = undefined;
		this.conn?._close();
	}
}
