import { createElement, type ReactNode } from 'react';
import { InlineMath as $ } from 'react-katex';

import { Buffer } from '../../buffer';
import { stripTrailingZero } from '../../util/string';
import { Transaction } from './transaction';
import { CATCOIN } from './util';

export class Block {
	height: number;
	hash: Buffer;
	raw: Uint8Array;

	magic: Buffer;
	prevHash: Buffer;
	merkleRoot: Buffer;
	time: Date;
	difficulty: bigint;
	nonce: bigint;
	txns: Transaction[];

	reward: number;
	total: number;

	constructor(height: number, hash: Uint8Array, raw: Uint8Array) {
		this.height = height;
		this.hash = Buffer.fromUint8Array(hash);
		this.raw = raw;
		let view = new DataView(raw.buffer, raw.byteOffset, raw.byteLength);

		this.magic = Buffer.fromUint8Array(raw.subarray(0, 8));
		this.prevHash = Buffer.fromUint8Array(raw.subarray(8, 40));
		this.merkleRoot = Buffer.fromUint8Array(raw.subarray(40, 72));
		this.time = new Date(Number(view.getBigUint64(72, false) / 1000000n));
		this.difficulty = view.getBigUint64(80, false);
		this.nonce = view.getBigUint64(88, false);

		const ptr: [Uint8Array] = [raw.subarray(96)];
		if (!ptr[0].length) { // head only
			this.txns = [];
			this.reward = NaN;
			this.total = NaN;
			return;
		}
		this.txns = [new Transaction(ptr)];
		this.reward = Number(this.txns[0].getReward()) / CATCOIN;

		view = new DataView(ptr[0].buffer, ptr[0].byteOffset, ptr[0].byteLength);
		const _txn = view.getUint32(0, false);
		ptr[0] = ptr[0].subarray(4);
		for (let i = 0; i < _txn; ++i) {
			this.txns.push(new Transaction(ptr));
		}

		this.total = Number(this.txns.slice(1).reduce((value, txn) => value + txn.getReward(), 0n)) / CATCOIN;
	}

	getDifficulty(): ReactNode {
		const r = Number(this.difficulty) / (2 ** 64), b = Math.floor(Math.log2(r) + 1e-3);
		return <$ math={`< ${stripTrailingZero((r * 2 ** - b).toPrecision(6))
			} \\times 2^{8 \\cdot ${this.difficulty & 255n}${b || ''}}`} />
	}
}
