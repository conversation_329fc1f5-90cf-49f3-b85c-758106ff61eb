import { Buffer } from '../../buffer';

export class TxIn {
	hash: Buffer;
	idx: number;

	constructor(ptr: [Uint8Array]) {
		const view = new DataView(ptr[0].buffer, ptr[0].byteOffset, ptr[0].byteLength);
		this.hash = Buffer.fromUint8Array(ptr[0].subarray(0, 32));
		this.idx = view.getUint32(32, false);
		ptr[0] = ptr[0].subarray(36);
	}
}

export class TxOut {
	value: bigint;
	pubKey: Buffer;
	fingerPrint?: Buffer;

	constructor(ptr: [Uint8Array]) {
		const view = new DataView(ptr[0].buffer, ptr[0].byteOffset, ptr[0].byteLength);
		this.value = view.getBigUint64(0, false);
		const pkLen = view.getUint32(8, false);
		this.pubKey = Buffer.fromUint8Array(ptr[0].subarray(12, 12 + pkLen));
		this.computeFingerPrint();
		ptr[0] = ptr[0].subarray(12 + pkLen);
	}

	computeFingerPrint() {
		let x;
		switch (this.pubKey.asUint8Array()[0]) {
			case 0x98:
				x = new Uint8Array([0x99, ...this.pubKey.asUint8Array()]);
				x[1] = 0;
				break;
			case 0x99:
				x = this.pubKey.asUint8Array();
				break;
			default:
				return;
		}
		crypto.subtle.digest('SHA-1', x).then(digest =>
			this.fingerPrint = Buffer.fromUint8Array(new Uint8Array(digest, 12))
		);
	}
}

export class Transaction {
	hash: Buffer;
	raw: Uint8Array;

	ins: TxIn[];
	outs: TxOut[];
	time: Date;
	signature: Uint8Array;

	constructor(ptr: [Uint8Array]) {
		const origin = ptr[0];

		let view = new DataView(ptr[0].buffer, ptr[0].byteOffset, ptr[0].byteLength);
		const _in = view.getUint32(0, false);
		ptr[0] = ptr[0].subarray(4);
		this.ins = Array.from({ length: _in }, () => new TxIn(ptr));

		view = new DataView(ptr[0].buffer, ptr[0].byteOffset, ptr[0].byteLength);
		const _out = view.getUint32(0, false);
		ptr[0] = ptr[0].subarray(4);
		this.outs = Array.from({ length: _out }, () => new TxOut(ptr));

		view = new DataView(ptr[0].buffer, ptr[0].byteOffset, ptr[0].byteLength);
		this.time = new Date(Number(view.getBigUint64(0, false) / 1000000n));
		const sigLen = view.getUint32(8, false);
		this.signature = ptr[0].subarray(12, 12 + sigLen);
		ptr[0] = ptr[0].subarray(12 + sigLen);

		this.raw = origin.subarray(0, ptr[0].byteOffset - origin.byteOffset);
		this.hash = Buffer.fromUint8Array(this.raw).sha3_256();
	}

	getReward() {
		return this.outs.reduce((value, out) => value + out.value, 0n);
	}
}
