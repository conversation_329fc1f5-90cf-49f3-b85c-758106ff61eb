// from @hcaptcha/react-hcaptcha
import { Component, createElement, createRef, type RefObject } from 'react';

import * as hcaptcha from '../../modules/hcaptcha/api';

interface HCaptchaState {
	isRemoved: boolean;
	captchaId: string;
}

interface HCaptchaProps {
	onExpire?: () => void;
	onOpen?: () => void;
	onClose?: () => void;
	onChalExpired?: () => void;
	onError?: (event: string) => void;
	onVerify?: (token: string, ekey: string) => void;
	onLoad?: () => void;
	languageOverride?: string;
	sitekey: string;
	size?: "normal" | "compact" | "invisible";
	theme?: "light" | "dark";
	tabIndex?: number;
	reCaptchaCompat?: boolean;
	loadAsync?: boolean;
	scriptLocation?: HTMLElement | null;
}

interface ExecuteResponse {
	response: string;
	key: string;
}

export default class HCaptcha extends Component<HCaptchaProps, HCaptchaState> {
	#ref: RefObject<HTMLDivElement | null>
	static displayName: string;

	constructor(props: HCaptchaProps) {
		super(props);

		this.renderCaptcha = this.renderCaptcha.bind(this);
		this.resetCaptcha = this.resetCaptcha.bind(this);
		this.removeCaptcha = this.removeCaptcha.bind(this);
		this.isReady = this.isReady.bind(this);

		this.handleSubmit = this.handleSubmit.bind(this);
		this.handleExpire = this.handleExpire.bind(this);
		this.handleError = this.handleError.bind(this);
		this.handleOpen = this.handleOpen.bind(this);
		this.handleClose = this.handleClose.bind(this);
		this.handleChallengeExpired = this.handleChallengeExpired.bind(this);

		this.#ref = createRef();

		this.state = {
			isRemoved: false,
			captchaId: ''
		}
	}

	componentDidMount() {
		this.renderCaptcha();
	}

	componentWillUnmount() {
		const { captchaId } = this.state;

		if (!this.isReady()) {
			return;
		}

		hcaptcha.reset(captchaId);
		hcaptcha.remove(captchaId);
	}

	shouldComponentUpdate(_nextProps: HCaptchaProps, nextState: HCaptchaState) {
		return this.state.isRemoved === nextState.isRemoved;
	}

	componentDidUpdate(prevProps: HCaptchaProps) {
		const keys = ['sitekey', 'size', 'theme', 'tabIndex', 'languageOverride'] as const;
		const match = keys.every(key => prevProps[key] === this.props[key]);

		if (!match) {
			this.removeCaptcha(this.renderCaptcha);
		}
	}

	renderCaptcha() {
		const renderParams = Object.assign({
			"open-callback": this.handleOpen,
			"close-callback": this.handleClose,
			"error-callback": this.handleError,
			"chalexpired-callback": this.handleChallengeExpired,
			"expired-callback": this.handleExpire,
			"callback": this.handleSubmit,
		}, this.props, {
			hl: this.props.languageOverride,
			languageOverride: undefined
		});

		const captchaId = hcaptcha.render(this.#ref.current!, renderParams)!;
		this.#ref.current!.querySelector('iframe')!.style.height = '76px';

		this.setState({ isRemoved: false, captchaId });
	}

	resetCaptcha() {
		if (!this.isReady()) {
			return;
		}

		hcaptcha.reset(this.state.captchaId)
	}

	removeCaptcha(callback?: () => void) {
		const { captchaId } = this.state;

		if (!this.isReady()) {
			return;
		}

		this.setState({ isRemoved: true }, () => {
			hcaptcha.remove(captchaId);
			callback && callback();
		});
	}

	handleSubmit() {
		const { onVerify } = this.props;
		const { isRemoved, captchaId } = this.state;

		if (isRemoved) return;

		const token = hcaptcha.getResponse(captchaId);
		const ekey = hcaptcha.getRespKey(captchaId);

		if (onVerify) onVerify(token, ekey);
	}

	handleExpire() {
		const { onExpire } = this.props;

		if (!this.isReady()) {
			return;
		}

		hcaptcha.reset(this.state.captchaId);

		if (onExpire) onExpire();
	}

	handleError(event: string) {
		const { onError } = this.props;

		if (this.isReady()) {
			hcaptcha.reset(this.state.captchaId);
		}

		if (onError) onError(event);
	}

	isReady() {
		return !this.state.isRemoved;
	}

	handleOpen() {
		if (!this.isReady() || !this.props.onOpen) {
			return;
		}

		this.props.onOpen();
	}

	handleClose() {
		if (!this.isReady() || !this.props.onClose) {
			return;
		}

		this.props.onClose();
	}

	handleChallengeExpired() {
		if (!this.isReady() || !this.props.onChalExpired) {
			return;
		}

		this.props.onChalExpired();
	}

	execute(opts: ConfigExecute | null = null): void | Promise<ExecuteResponse> {
		if (!this.isReady()) {
			return;
		}

		if (opts && typeof opts !== "object") {
			opts = null;
		}

		return hcaptcha.execute(this.state.captchaId, opts!);
	}

	setData(data: ConfigSetData | null) {
		if (!this.isReady()) {
			return;
		}

		if (data && typeof data !== "object") {
			data = null;
		}

		hcaptcha.setData(this.state.captchaId, data!);
	}

	getResponse() {
		return hcaptcha.getResponse(this.state.captchaId);
	}

	getRespKey() {
		return hcaptcha.getRespKey(this.state.captchaId);
	}

	render() {
		return <div ref={this.#ref} style={{ lineHeight: 0 }} />;
	}
}

HCaptcha.displayName = 'HCaptcha';
