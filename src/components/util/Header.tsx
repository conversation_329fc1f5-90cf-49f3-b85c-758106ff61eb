import { createElement, Fragment } from 'react';
import { Container, Dropdown, Icon, Menu } from 'semantic-ui-react';

import User, { UserCenter } from '../../user';
import menu from '../../menu';

const Header: React.FC = () => {
	const user = UserCenter();

	function logout() {
		User.oneClickLogout();
		// if (await Modals.clearSign().show() === true) {
		//	localStorage.removeItem('signFunc');
		// }
	}

	return (
		<Menu fixed="top" borderless>
			<Container>
				{
					Object.entries(menu).map(([key, { icon, name, to }]) => {
						const active = location.pathname === to;
						return (
							<Menu.Item
								active={active}
								content={name}
								href={active ? null : to}
								icon={icon}
								key={key}
							/>
						)
					})
				}
				<Menu.Menu position="right">
					{user
						? <div className="ui simple dropdown item">
							{user.uid}
							<Icon name="dropdown" />
							<div className="menu">
								<Menu.Item content="修改信息" href="/change" icon="edit" />
								<Menu.Item content="偏好设置" href="/preference" icon="setting" />
								<Dropdown.Divider />
								<Menu.Item content="登录信息配置" href="/login" icon="sign in" />
								<Menu.Item content="注册新用户" href="/register" icon="add user" />
								<Menu.Item as="div" content="登出" icon="log out" onClick={logout} />
							</div>
						</div>
						: <>
							<Menu.Item content="登录" href="/login" icon="sign in" />
							<Menu.Item content="注册" href="/register" icon="add user" />
							<Menu.Item content="偏好设置" href="/preference" icon="setting" />
						</>
					}
				</Menu.Menu>
			</Container>
		</Menu>
	);
}

Header.displayName = 'Header';

export default Header;
