import { createElement, useState, type ReactNode } from 'react';
import { Icon, Loader, Modal, Table } from 'semantic-ui-react';

import type { HistoryData } from '../../models/leaderboard/history';
import { getHistory } from '../../modules/leaderboard/api/history';
import { date2str } from '../../util/date';
import { average } from '../../util/nt';
import { stripTrailingZero } from '../../util/string';
import ErrorMessage from '../util/ErrorMessage';

const littlePadding = {
	paddingTop: '.5em',
	paddingBottom: '.5em'
}

interface HistoryTableProps {
	readonly data: HistoryData[];
}

interface HistoryModalProps {
	readonly user: string;
	readonly trigger: ReactNode;
}

export const HistoryTable: React.FC<HistoryTableProps> = props => {
	return (
		<Table textAlign="center" celled selectable unstackable>
			<Table.Header>
				<Table.Row>
					<Table.HeaderCell rowSpan={2}>分数</Table.HeaderCell>
					<Table.HeaderCell rowSpan={2}>平均结果</Table.HeaderCell>
					<Table.HeaderCell colSpan={3} style={littlePadding}>各项结果</Table.HeaderCell>
					<Table.HeaderCell rowSpan={2}>提交时间</Table.HeaderCell>
				</Table.Row>
				<Table.Row>
					<Table.HeaderCell style={{ display: 'none' }} /* CSS Hack */></Table.HeaderCell>
					<Table.HeaderCell style={littlePadding}>Mountain</Table.HeaderCell>
					<Table.HeaderCell style={littlePadding}>Sky</Table.HeaderCell>
					<Table.HeaderCell style={littlePadding}>Water</Table.HeaderCell>
				</Table.Row>
			</Table.Header>
			<Table.Body>
				{
					props.data.map(entry => (
						<Table.Row key={entry.time}>
							<Table.Cell>{entry.score}</Table.Cell>
							<Table.Cell>{stripTrailingZero(average(entry.subs).toFixed(2))}</Table.Cell>
							<Table.Cell>{entry.subs[0]}</Table.Cell>
							<Table.Cell>{entry.subs[1]}</Table.Cell>
							<Table.Cell>{entry.subs[2]}</Table.Cell>
							<Table.Cell>{date2str(new Date(entry.time * 1e3))}</Table.Cell>
						</Table.Row>
					))
				}
			</Table.Body>
		</Table>
	);
};

HistoryTable.displayName = 'HistoryTable';

export const HistoryModal: React.FC<HistoryModalProps> = props => {
	const [loaded, setLoaded] = useState(false);
	const [historyData, setHistoryData] = useState<HistoryData[]>([]);
	const [error, setError] = useState<unknown>(null);

	async function loadData() {
		setLoaded(false);
		setError(null);
		try {
			const data = await getHistory(props.user);
			setLoaded(true);
			setHistoryData(data);
		} catch (e) {
			setError(e);
		}
	}

	return (
		<Modal
			closeOnEscape
			closeOnDimmerClick
			closeIcon
			dimmer={{ inverted: true }}
			onMount={loadData}
			size="large"
			trigger={props.trigger}
		>
			<Modal.Header style={{ fontSize: '1.42857143em' }}>
				{props.user}
				&ensp;
				<Icon name="redo" onClick={loadData} style={{ fontSize: '1rem' }} />
			</Modal.Header>
			<Modal.Content>
				{error
					? <ErrorMessage err={error} />
					: loaded
						? <HistoryTable data={historyData} />
						: (
							<div style={{ height: '3em', position: 'relative' }}>
								<Loader active />
							</div>
						)
				}
			</Modal.Content>
		</Modal>
	);
}

HistoryModal.displayName = 'HistoryModal';
