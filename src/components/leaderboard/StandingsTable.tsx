import { createContext, createElement, useContext, type MouseEvent } from 'react';
import { Button, Image, Table } from 'semantic-ui-react';

import type { LeaderBoardEntry } from '../../models/leaderboard/leaderboard';
import type { SubmitData } from '../../models/leaderboard/submitdata';
import { date2str } from '../../util/date';
import { average } from '../../util/nt';
import { stripTrailingZero } from '../../util/string';
import TableLoader from '../util/TableLoader';
import { HistoryModal } from './History';

const littlePadding = {
	paddingTop: '.5em',
	paddingBottom: '.5em'
}

interface LeaderBoardEntryRowProps {
	readonly data: LeaderBoardEntry;
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	readonly [key: string]: any;
}

interface StandingsTableProps {
	readonly loaded: boolean;
	readonly data: LeaderBoardEntry[];
}

export interface Callbacks {
	vote?: (user: string) => Promise<void>;
	submit?: (data: SubmitData) => Promise<void>;
}

export const Context = createContext<Callbacks>({});

export const LeaderBoardEntryRow: React.FC<LeaderBoardEntryRowProps> = props => {
	const { vote } = useContext(Context);

	function handleClick(e: MouseEvent) {
		e.stopPropagation();
		// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
		vote!(props.data.user);
	}

	return (
		// eslint-disable-next-line react/prop-types
		<Table.Row onClick={props.onClick}>
			<Table.Cell textAlign="left">
				{
					props.data.avatar?.startsWith?.('iVBORw0KGg') // PNG header
					&& <Image
						avatar
						src={`data:image/png;base64,${props.data.avatar}`}
						alt={props.data.user}
					/>
				}
				{props.data.user}
			</Table.Cell>
			<Table.Cell>{props.data.score}</Table.Cell>
			<Table.Cell>{date2str(new Date(props.data.time * 1e3))}</Table.Cell>
			<Table.Cell>{stripTrailingZero(average(props.data.subs).toFixed(2))}</Table.Cell>
			<Table.Cell>{props.data.subs[0]}</Table.Cell>
			<Table.Cell>{props.data.subs[1]}</Table.Cell>
			<Table.Cell>{props.data.subs[2]}</Table.Cell>
			<Table.Cell>
				<Button
					circular
					compact
					icon="thumbs up"
					label={{
						as: 'div',
						basic: true,
						circular: true,
						content: props.data.votes,
						pointing: null
					}}
					onClick={handleClick}
				/>
			</Table.Cell>
		</Table.Row>
	);
}

LeaderBoardEntryRow.displayName = 'LeaderBoardEntryRow';

export const LeaderBoardEntryRowWithHistory: React.FC<LeaderBoardEntryRowProps> = props => {
	return (
		<HistoryModal user={props.data.user} trigger={
			<LeaderBoardEntryRow {...props} />
		} />
	);
}

LeaderBoardEntryRowWithHistory.displayName = 'LeaderBoardEntryRowWithHistory';

export const StandingsTable: React.FC<StandingsTableProps> = props => {
	return (
		<Table textAlign="center" celled selectable unstackable>
			<Table.Header>
				<Table.Row>
					<Table.HeaderCell rowSpan={2}>ID</Table.HeaderCell>
					<Table.HeaderCell rowSpan={2}>分数</Table.HeaderCell>
					<Table.HeaderCell rowSpan={2}>最后提交时间</Table.HeaderCell>
					<Table.HeaderCell rowSpan={2}>平均结果</Table.HeaderCell>
					<Table.HeaderCell colSpan={3} style={littlePadding}>各项结果</Table.HeaderCell>
					<Table.HeaderCell rowSpan={2}>投票</Table.HeaderCell>
				</Table.Row>
				<Table.Row>
					<Table.HeaderCell style={{ display: 'none' }} /* CSS Hack */></Table.HeaderCell>
					<Table.HeaderCell style={littlePadding}>Mountain</Table.HeaderCell>
					<Table.HeaderCell style={littlePadding}>Sky</Table.HeaderCell>
					<Table.HeaderCell style={littlePadding}>Water</Table.HeaderCell>
				</Table.Row>
			</Table.Header>
			<Table.Body>
				{props.loaded
					? props.data.map(
						entry => <LeaderBoardEntryRowWithHistory key={entry.user} data={entry} />
					)
					: <TableLoader colSpan={8} />
				}
			</Table.Body>
		</Table>
	);
}

StandingsTable.displayName = 'StandingsTable';
