import { ChangeEvent, createElement, useContext, useEffect, useState } from 'react';
import { Button, Form, Input, TextArea } from 'semantic-ui-react';

import { <PERSON>uffer } from '../../buffer';
import { basename } from '../../util/string';
import ErrorMessage from '../util/ErrorMessage';
import { Context } from './StandingsTable';

const SubmitForm: React.FC = () => {
	const [user, setUser] = useState('');
	const [content, setContent] = useState('');
	const [file, setFile] = useState<File | null>(null);
	const [fileName, setFileName] = useState('');
	const [uploaderProxy, setUploaderProxy] = useState<HTMLInputElement | null>(null);
	const [error, setError] = useState<unknown>(null);
	const { submit } = useContext(Context);

	useEffect(() => {
		const input = document.createElement('input');
		input.accept = 'image/png,.png'
		input.type = 'file';
		input.addEventListener('input', () => {
			setFileName(basename(input.value));
			setFile(input.files?.length ? input.files[0] : null);
			setError(null);
		});
		setUploaderProxy(input);
	}, []);

	function uploadFile() {
		if (!uploaderProxy) return;
		uploaderProxy.click();
	}

	function handleChange(_e: ChangeEvent, { name, value }: { name: 'ID' | 'result', value: string }) {
		setError(null);
		switch (name) {
			case 'ID': {
				setUser(value);
				break;
			}
			case 'result': {
				setContent(value);
				break;
			}
			default: {
				const exhaustiveCmh: never = name;
				return exhaustiveCmh;
			}
		}
	}

	async function onSubmit() {
		const avatar = (file
			? Buffer
				.fromArrayBuffer(await file.arrayBuffer())
				.asBase64()
			: ''
		);
		try {
			// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
			await submit!({ user, avatar, content });
			setUser('');
			setContent('');
			setFile(null);
			setFileName('');
			setError(null);
			if (uploaderProxy) uploaderProxy.value = '';
		} catch (e) {
			setError(e);
		}
	}

	return (
		<Form as="div" error={!!error}>
			<Form.Input
				icon="id card"
				iconPosition="left"
				label="ID"
				name="ID"
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				onChange={handleChange as any}
				placeholder="例: 2021010769"
				value={user}
			/>
			<Form.Field>
				<label>result.txt</label>
				<TextArea
					className="small"
					name="result"
					// eslint-disable-next-line @typescript-eslint/no-explicit-any
					onChange={handleChange as any}
					placeholder={'例:\n1,1,1\n1,1,1\n<以下省略 997 行>\n1,1,1'}
					rows=""
					value={content}
				/>
			</Form.Field>
			<Form.Group style={{ alignItems: 'center' }}>
				<Form.Field inline style={{ display: 'flex', width: '50%', alignItems: 'center' }}>
					<label>头像</label>
					<Input icon="user circle" iconPosition="left" placeholder="(png 格式文件名，自动填充，可留空)" readOnly style={{ flex: 1 }} value={fileName} />
				</Form.Field>
				<Button content="选择文件" icon="upload" onClick={uploadFile} primary />
			</Form.Group>
			<div style={{ marginTop: '1em' }}>
				<Button color="pink" content="提交" icon="send" onClick={onSubmit} />
			</div>
			{
				error ? <ErrorMessage err={error} /> : null
			}
		</Form>
	);
}

SubmitForm.displayName = 'SubmitForm';

export default SubmitForm;
