import { createElement, Fragment, type ReactNode } from 'react';
import { Accordion, Header, List, Modal, Segment, Table } from 'semantic-ui-react';

import type { Block } from '../../modules/catcoin/block';
import { date2str } from '../../util/date';
import { Transaction } from '../../modules/catcoin/transaction';
import { CATCOIN, shortenHash } from '../../modules/catcoin/util';

interface TransactionPanelProps {
	readonly txns: Transaction[];
}

interface BlockModalProps {
	readonly block: Block;
	readonly children: ReactNode;
}

export const TransactionPanel: React.FC<TransactionPanelProps> = props => {
	const panels = props.txns.map((txn, idx) => ({
		key: txn.hash.asHex(),
		title: {
			content: <>{idx ? `交易 #${idx}: ` : '铸币交易: '}{shortenHash(txn.hash.asHex())}</>,
		},
		content: {
			content: (
				<>
					<List divided relaxed>
						<List.Item header="Hash" content={<samp>0x{txn.hash.asHex()}</samp>} />
						<List.Item header="时间" content={date2str(txn.time)} />
						<List.Item header="金额" content={`${Number(txn.getReward()) / CATCOIN} CTC`} />
						<List.Item header="交易大小" content={`${txn.raw.length} B`} />
						<List.Item header="签名长度" content={`${txn.signature.length} B`} />
					</List>
					<Header dividing size="small" content="输入" />
					<Table textAlign="center" compact selectable unstackable>
						<Table.Header>
							<Table.Row>
								<Table.HeaderCell>#</Table.HeaderCell>
								<Table.HeaderCell>源交易 Hash</Table.HeaderCell>
								<Table.HeaderCell>源交易输出位次</Table.HeaderCell>
							</Table.Row>
						</Table.Header>
						<Table.Body>
							{txn.ins.map((input, idx) => (
								<Table.Row key={idx}>
									<Table.Cell content={idx} />
									<Table.Cell content={<samp>0x{input.hash.asHex()}</samp>} />
									<Table.Cell content={input.idx} />
								</Table.Row>
							))}
						</Table.Body>
					</Table>
					<Header dividing size="small" content="输出" />
					<Table textAlign="center" compact selectable unstackable>
						<Table.Header>
							<Table.Row>
								<Table.HeaderCell>#</Table.HeaderCell>
								<Table.HeaderCell>金额</Table.HeaderCell>
								<Table.HeaderCell>交易对象 Hash 指纹</Table.HeaderCell>
							</Table.Row>
						</Table.Header>
						<Table.Body>
							{txn.outs.map((out, idx) => (
								<Table.Row key={idx}>
									<Table.Cell content={idx} />
									<Table.Cell content={`${Number(out.value) / CATCOIN} CTC`} />
									<Table.Cell content={<samp>{out.fingerPrint?.asHex() ?? '<error>'}</samp>} />
								</Table.Row>
							))}
						</Table.Body>
					</Table>
				</>
			)
		},
	}));

	return (
		<Accordion
			exclusive={false}
			fluid
			panels={panels}
			styled
		/>
	)
}
export const BlockModal: React.FC<BlockModalProps> = props => {
	return (
		<Modal
			closeOnEscape
			closeOnDimmerClick
			closeIcon
			dimmer={{ inverted: true }}
			size="large"
			trigger={props.children}
		>
			<Modal.Header style={{ fontSize: '1.42857143em' }}>
				Catcoin Block #{props.block.height}
			</Modal.Header>
			<Modal.Content>
				<Header
					block
					size="medium"
					content="块信息"
					attached="top"
				/>
				<Segment attached="bottom">
					<List divided relaxed>
						<List.Item header="块版本" content={props.block.magic.asUint8Array()[7]} />
						<List.Item header="Hash" content={<samp>0x{props.block.hash.asHex()}</samp>} />
						<List.Item header="时间" content={date2str(props.block.time)} />
						<List.Item header="难度" content={props.block.getDifficulty()} />
						{props.block.txns.length ? <List.Item header="交易数" content={props.block.txns.length - 1} /> : null}
						<List.Item header="Nonce" content={<samp>0x{props.block.nonce.toString(16).padStart(16, '0')}</samp>} />
						<List.Item header="块大小" content={`${props.block.raw.length} B`} />
						{...(isNaN(props.block.reward) ? [] : [
							<List.Item header="奖励金额" content={`${props.block.reward} CTC`} />,
							<List.Item header="总输入" content={`${props.block.total} CTC`} />,
							<List.Item header="总输出" content={`${props.block.total + props.block.reward} CTC`} />,
						])}
						<List.Item header="Merkle 根" content={<samp>0x{props.block.merkleRoot.asHex()}</samp>} />
					</List>
				</Segment>
				{...(props.block.txns.length ? [
					<Header
						block
						size="medium"
						content="交易信息"
						attached="top"
					/>,
					<TransactionPanel txns={props.block.txns} />
				] : [])}
			</Modal.Content>
		</Modal>
	);
}

BlockModal.displayName = 'BlockModal';
