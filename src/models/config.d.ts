import type { InitUserConfig } from './user';

export type ConfigEntry = Config | boolean | number | string | boolean;

// can't use `Record<string, ConfigEntry>` due to circular references
export interface Config {
	[key: string]: ConfigEntry;
}

// export interface ResourceConfig {
// 	path: string;
// 	integrity?: string;
// }

// export type AjaxConfig = Partial<Record<'css' | 'mjs' | 'js', Record<string, ResourceConfig>>>;

export interface RenderConfig {
	initUser?: InitUserConfig;
}
