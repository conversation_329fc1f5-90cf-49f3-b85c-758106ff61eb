import type { Buffer } from '../buffer';
import type { AESEncryptedData } from './crypto';

export interface WrappedObject<T> {
	uid: string;
	sign: <PERSON><PERSON><PERSON>;
	data: T;
}

export interface EmailDetails {
	address: string;
	extra: string;
	public: boolean;
}

interface EncryptedEmailDetailsPublic {
	address: string;
	extra: AESEncryptedData;
	public: true;
}

interface EncryptedEmailDetailsPrivate {
	address: AESEncryptedData;
	extra: AESEncryptedData;
	public: false;
}

export type EncryptedEmailDetails = EncryptedEmailDetailsPublic | EncryptedEmailDetailsPrivate;

export interface InitUserConfig {
	fields?: string[];
	requireSign?: boolean;
}
