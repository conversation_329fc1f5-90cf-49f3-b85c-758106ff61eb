import '@hcaptcha/types';
import 'socket.io-client/build/esm/manager';

export type Mutable<T> = {
	-readonly [P in keyof T]: T[P];
}

declare global {
	function isNaN(value: unknown): boolean;

	interface NumberConstructor {
		isSafeInteger(number: unknown): number is number;
	}

	interface Uint8ArrayConstructor {
		fromHex(hex: string): Uint8Array;
		fromBase64(base64: string): Uint8Array;
	}

	interface Uint8Array {
		toHex(): string;
		toBase64(): string;
	}
}

declare module 'module-trie' {
	type char = string & { length: 1 }
	interface TrieNode {
		[char: char]: TrieNode;
		su?: string;
		vl?: number;
	}
	const moduleTrie: TrieNode;
	const moduleList: string[];
}
