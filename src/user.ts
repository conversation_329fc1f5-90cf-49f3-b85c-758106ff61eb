import assert from 'nanoassert';
import { create, type Store<PERSON><PERSON>, type UseBoundStore } from 'zustand';

import { <PERSON><PERSON><PERSON>, FixedLengthBuffer } from './buffer';
import type { base64String } from './models/buffer';
import type { EmailDetails, EncryptedEmailDetails, InitUserConfig, WrappedObject } from './models/user';
import { ResponseError } from './models/web';
import { ConfigPath } from './util/config';
import { codecMessage, codecShortMessage } from './util/crypto';
import { sortedStringify } from './util/json';
import { PowerMod, Zn } from './util/nt';
import { getIdentifierNotUsed } from './util/string';
import { AsyncFunction } from './util/type';
import { MAX_DATE, POST, runtimeImport, shouldSaveCookie } from './util/web';

type SignFunction = (data: bigint) => bigint | Promise<bigint>;

export const regUID = /^[\w-]{1,20}$/;
const preCheckPath = new ConfigPath(['preCheck']);

export default class User {
	uid: string;
	modulus: Buffer;
	exponent: number;
	token?: Buffer;
	emails: EmailDetails[];
	ajax?: object;
	#emails?: {
		key: base64String;
		data: EncryptedEmailDetails[];
	};
	static signFunc: SignFunction = x => x;
	static initConfig?: InitUserConfig;

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	constructor(config: any) {
		this.uid = config.uid;
		this.modulus = config.modulus;
		this.exponent = config.exponent;
		this.token = config.token;
		this.emails = config instanceof User ? config.emails : [];
		this.#emails = config.emails;
		this.ajax = config.ajax;
	}

	/******** member functions ********/
	encrypt(data: bigint) {
		return PowerMod(data, BigInt(this.exponent), this.modulus.asBigInt());
	}

	verify(message: bigint, sign: bigint) {
		return 0n <= sign && sign < this.modulus.asBigInt() && this.encrypt(sign) === message;
	}

	async signRaw(buffer: Buffer) { // sign( [buffer] % [modulus] )
		const data = buffer.asBigInt() % this.modulus.asBigInt();
		return Buffer.fromBigInt(await User.signFunc(data));
	}

	async sign<T>(data: T, refreshToken = true): Promise<WrappedObject<T>> {
		if (refreshToken) await this.refreshToken();
		const
			content = Buffer.concat(Buffer.fromUtf8(sortedStringify(data)), this.token!),
			digest = await content.sha256(),
			sign = await this.signRaw(digest);
		return { uid: this.uid, data, sign };
	}

	async initSign(): Promise<true | Error> {
		try {
			const rawCode = localStorage.getItem('signFunc') ?? '';
			const import$ = getIdentifierNotUsed(rawCode);
			const code = rawCode.replace(/(?<![\w$])import\s*(?=[(`])/g, import$);
			User.signFunc = await new AsyncFunction(import$, '"use strict";' + code)(runtimeImport);
			return true;
		} catch (e) {
			return <Error>e;
		}
	}

	async preCheckSign(n: number): Promise<number | false | Error> {
		try {
			let consumption = 0;
			for (let t = 0; t < n; ++t) {
				const
					message = Zn(this.modulus.asBigInt()),
					begin = performance.now(),
					sign = await User.signFunc(message),
					end = performance.now();
				if (!this.verify(message, sign)) return false;
				consumption += end - begin;
			}
			return consumption / n;
		} catch (e) {
			return <Error>e;
		}
	}

	async refreshToken() {
		const response = await User.#post(this.uid, ['token']);
		this.token = Buffer.fromBase64(response.token);
	}

	async handleEmails() {
		if (this.#emails && Object.hasOwn(this.#emails, 'key')) {
			const
				key_e = Buffer.fromBase64(this.#emails.key).asBigInt() % this.modulus.asBigInt(),
				key = await User.signFunc(key_e),
				originalDatas = this.#emails.data;
			this.emails = await Promise.all(
				originalDatas.map(async entry => {
					const promiseExtra =
						codecMessage(
							Buffer.fromBase64(entry.extra.data),
							codecShortMessage(
								FixedLengthBuffer.fromBase64(entry.extra.ivKey), key
							),
							true
						).then(extra => extra.asUtf8())

					const promise = entry.public
						? promiseExtra.then(extra => [entry.address, extra])
						: Promise.all([
							codecMessage(
								Buffer.fromBase64(entry.address.data),
								codecShortMessage(
									FixedLengthBuffer.fromBase64(entry.address.ivKey), key
								),
								true
							).then(address => address.asUtf8()),
							promiseExtra
						]);

					const [address, extra] = await promise;
					return { address, extra, public: entry.public }
				})
			);
		}
	}

	/******** static functions ********/
	static #post(uid: string, fields: string[]) {
		return POST('/user', { uid, fields });
	}

	static async updateStorages(user: User | null) {
		if (user) {
			localStorage.setItem('uid', user.uid);
			shouldSaveCookie(user.uid)
				? cookieStore.set({ name: 'uid', value: user.uid, expires: MAX_DATE, domain: null })
				: cookieStore.delete('uid');
		} else {
			localStorage.removeItem('uid');
			cookieStore.delete('uid');
		}
	}

	static setInitConfig(config: InitUserConfig) {
		User.initConfig = config;
	}

	static async initCurrentUser(uid?: string) {
		const config = User.initConfig!;

		if (!uid) {
			uid = localStorage.getItem('uid') ?? (await cookieStore.get('uid'))?.value;
		}

		if (!(typeof uid === 'string' && regUID.test(uid))) return;

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		(<any>UserCenter).setState(/* dummy user */ new User({ uid }), true, false);

		const fields = [...new Set([...(config.fields ?? []), 'modulus', 'exponent'])];

		let user;
		try {
			const response = await User.#post(uid, fields);
			if (response == null) {
				return UserCenter.setState(null);
			}
			response.modulus = Buffer.fromBase64(response.modulus);
			user = new User(response);
			UserCenter.setState(user);
		} catch (e) {
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			(<any>UserCenter).setState(null, true, false);
			const cause = (e as ResponseError)?.cause;
			if (Array.isArray(e)) {
				// modal
			}
			return;
		}

									/* tmp ***/ const result = await user.initSign();
		/*
				const
					preCheck = preCheckPath.get(ConfigCenter.getState().config),
					T = clipInt(preCheck, 0, 10, 1);
				let error = null;

				const result = await user.initSign();
				if (result !== true)
					error = err2lines(result);
				else if (typeof User.signFunc !== 'function')
					error = '未返回函数';
				else {
					const cResult = await user.preCheckSign(T);
					if (cResult === false)
						error = '无效签名';
					else if (typeof cResult !== 'number')
						error = err2lines(cResult);
				}

				if (error != null) {
					//Modals.reconfigSignFunc().sfError = error;
					if (config.requireSign && preCheck !== false) {
						//	if (await Modals.reconfigSignFunc().show() === true) return location.assign('/login');
					}
				}
		*/
	}

	static oneClickLogout() {
		UserCenter.setState(null);
	}
}

export let UserCenter: UseBoundStore<StoreApi<User | null>>;

export function init() {
	UserCenter = create(
		(set, get, api) => {
			assert(api.getState === get);
			assert(api.setState === set);
			api.setState = (user: User | null, replace = true, shouldSave = true) => {
				set(user, replace);
				if (shouldSave) User.updateStorages(user);
			};
			window.addEventListener('storage', event => {
				if (event.key === 'uid' && event.oldValue !== event.newValue) {
					if (event.newValue?.length)
						User.initCurrentUser(event.newValue);
					else {
						set(null, true);
						User.updateStorages(null);
					}
				}
			});
			return null;
		}
	)
}
