import { createElement, Fragment, useState } from 'react';
import { Header, Segment } from 'semantic-ui-react';

import Pagination from '../../components/util/Pagination';

const chordleList: React.FC = () => {
	const [page, setPage] = useState(1);

	return (
		<>
			<Header
				block
				as="h3"
				content="Chordle 谜题列表"
				attached="top"
			/>
			<Segment attached="bottom" className="form">
				<Pagination page={page} total={100} onPageChange={setPage} />
			</Segment>
		</>
	);
}

chordleList.displayName = 'chordleList';

export default chordleList;
