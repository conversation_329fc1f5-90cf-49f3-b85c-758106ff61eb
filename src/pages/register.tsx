import { createElement, Fragment, useEffect } from 'react';
import { InlineMath as $ } from 'react-katex';
import { Button, Form, Header, Label, Message, Segment } from 'semantic-ui-react';
import { useStore } from 'zustand';

import { Buffer } from '../buffer';
import HCaptcha from '../components/hcaptcha/hcaptcha';
import type { ResponseError } from '../models/web';
import FormErrorMessage from '../modules/form/components/FormErrorMessage';
import TextArea from '../modules/form/components/TextArea';
import TextInput from '../modules/form/components/TextInput';
import ExponentField from '../modules/form/field/ExponentField';
import ModulusField from '../modules/form/field/ModulusField';
import UIDField from '../modules/form/field/UIDField';
import BaseForm from '../modules/form/form';
import User from '../user';
import { HCAPTCHA_EXPIRED_MAGIC, HCAPTCHA_SITEKEY, HCaptchaField } from '../util/hcaptcha';
import { backendErrorStackConvert, isObject } from '../util/type';
import { addToWhitelist, POST } from '../util/web';

const form = new BaseForm()
	.addField(new UIDField('uid', 'UID'))
	.addField(new ModulusField('modulus', '模数'))
	.addField(new ExponentField('exponent', '公钥指数'))
	.addField(new HCaptchaField('hCaptcha'))
	.withErrorTable({
		uid: {
			exists: uid => <>UID {uid} 已存在，请<a href="/login">登录</a>后<a href="/change">更改密钥</a></>
		},
		modulus: {
			smallFactor: p => `模数存在小素因子 ${p}`
		},
		hCaptcha: {
			absent: '请完成 CAPTCHA 验证',
			detailed: (...err) => `CAPTCHA 验证失败，错误代码：${err.join(', ')}`,
			failed: 'CAPTCHA 验证失败',
			timeout: 'CAPTCHA 验证请求超时，请重试'
		}
	})

async function handleRegister() {
	if (!form.validate()) return;
	const user = new User({
		uid: form.values.uid,
		modulus: Buffer.fromBigInt(BigInt(form.values.modulus)),
		exponent: Number(form.values.exponent),
	});
	try {
		await POST('/register', {
			uid: user.uid,
			modulus: user.modulus,
			exponent: user.exponent,
			hCaptchaResponse: form.values.hCaptcha,
		});
		addToWhitelist(user.uid);
		// modal TODO
		location.assign('/login');
	} catch (e) {
		const cause = (e as ResponseError)?.cause;
		if (Array.isArray(cause)) {
			form.setExtraError('', backendErrorStackConvert(cause)).rerender();
		} else if (isObject(cause) && typeof cause.field === 'string' && typeof cause.type === 'string') {
			if (cause.field === 'hCaptcha') globalThis.hcaptcha.reset();
			form.setExtraError(cause.field, cause.type, cause.arguments).rerender();
		} else {
			console.log(e);
			// use modal
		}
	}
}

const register: React.FC = () => {
	useStore(form);
	useEffect(form.errorScroll.bind(form));

	const hCaptchaField = form.fieldByName.hCaptcha;

	return (
		<>
			<Header
				block
				as="h3"
				content="注册"
				attached="top"
			/>
			<Segment attached="bottom" className={form.errorClass()}>
				<TextInput form={form} label="UID" name="uid" icon="id card" iconPosition="left" placeholder="例: root" maxLength={20} />
				<TextArea form={form} small label={<>模数 <$ math="n" /></>} name="modulus" placeholder="例: 998244353" />
				<TextInput form={form} label={<>指数 <$ math="e" /></>} name="exponent" icon="key" iconPosition="left" placeholder="例: 65537" />
				<Message warning size="small" style={{ display: 'block' }}>
					<p>可以通过<a href="/attachment/genKey.js" download="genKey.js">此链接</a>下载 genKey.js 脚本来生成或提取私钥文件 (如 OpenSSL 生成的) 中的 RSA 密钥。该脚本需要在 <a href="https://nodejs.org/" rel="noreferrer" target="_blank">Node.js</a> 环境下运行。</p>
					<p><strong>推荐通过上述方法生成密钥</strong>。如果无法满足，也可以在<a href="/genkey">此页面</a>生成 (为保证安全性不提供提取服务)，我们确保这个页面也是离线的。</p>
					<p>设模数为 <$ math="n" />，指数为 <$ math="e" />，请自觉保证 <$ math="\gcd \bigl( e, \varphi(n) \bigr) = 1" /> (服务器不接收私钥，因此无法检验该式子是否成立)。由该式子不成立造成的 “无法使用” 等后果均由用户承担。</p>
				</Message>
				<Form.Field>
					<HCaptcha
						sitekey={HCAPTCHA_SITEKEY}
						onExpire={() => form.setState('hCaptcha', HCAPTCHA_EXPIRED_MAGIC)}
						onVerify={response => form.setState('hCaptcha', response)}
					/>
					{hCaptchaField.prompt ? <Label content={hCaptchaField.prompt} prompt pointing /> : null}
				</Form.Field>
				<Button color="pink" content="注册" icon="add user" onClick={handleRegister} />
				<FormErrorMessage form={form} />
			</Segment>
		</>
	);
}

register.displayName = 'register';

export default register;
