import { createElement, Fragment, SyntheticEvent, useState } from 'react';
import { Button, Checkbox, CheckboxProps, Header, Segment, TextArea, TextAreaProps } from 'semantic-ui-react';

import { useConfig } from '../util/config';
import { chars2str } from '../util/string';

const
	ranges = [[48, 53], [65, 78], [97, 110], [19968, 30419]],
	rotate = (str: string) => chars2str(Array.from(str, ch => rotateOne(ch.codePointAt(0)!))),
	rotateOne = (x: number) => {
		for (const [l, m] of ranges) {
			const d = m - l, r = m + d;
			if (l <= x && x < m) return x + d;
			if (m <= x && x < r) return x - d;
		}
		return x;
	}

const rot10451: React.FC = () => {
	const
		[result, setResult] = useState(''),
		[autoCopy, setAutoCopy] = useConfig(['rot10451', 'autoCopy'], x => x === true),
		handleCopy = () => navigator.clipboard.writeText(result);

	const updateResult = (_e: SyntheticEvent, { value }: TextAreaProps) => {
		const res = rotate(value?.toString() ?? '');
		setResult(res);
		if (autoCopy) navigator.clipboard.writeText(res);
	}

	const handleAutoCopy = (_e: SyntheticEvent, { checked }: CheckboxProps) => {
		setAutoCopy(checked === true);
	}

	return (
		<>
			<Header
				block
				as="h3"
				content="在线 rot10451 编码"
				attached="top"
			/>
			<Segment attached="bottom" className="form">
				<TextArea onInput={updateResult} placeholder="原文" rows="" />
				<TextArea placeholder="编码结果" readOnly rows="" style={{ marginTop: '1rem' }} value={result} />
				<div style={{ marginTop: '1rem' }}>
					<Button content="复制" onClick={handleCopy} />
					<Checkbox
						checked={autoCopy}
						label="自动复制"
						onChange={handleAutoCopy}
						style={{ marginLeft: '1.5rem' }}
						toggle
					/>
				</div>
			</Segment>
		</>
	);
}

rot10451.displayName = 'rot10451';

export default rot10451;
