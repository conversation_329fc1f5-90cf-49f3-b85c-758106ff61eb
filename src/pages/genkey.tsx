import { createElement, Fragment, useEffect } from 'react';
import { InlineMath as $ } from 'react-katex';
import { Divider, Form, Header, Message, Segment } from 'semantic-ui-react';
import { useStore } from 'zustand';

import { Buffer } from '../buffer';
import FormErrorMessage from '../modules/form/components/FormErrorMessage';
import Text from '../modules/form/components/Text';
import TextArea from '../modules/form/components/TextArea';
import TextInput from '../modules/form/components/TextInput';
import Field from '../modules/form/field/Field';
import GenExponentField from '../modules/form/field/GenExponentField';
import BaseForm from '../modules/form/form';
import IntRule from '../modules/form/rule/IntRule';
import { err2lines } from '../util/type';
import { plainDownload } from '../util/web';

const
	{ subtle } = crypto,
	form = new BaseForm()
		.addField(
			new Field('length', '密钥长度').setOptional()
				.addRule(new IntRule(512, 4096))
		)
		.addField(new GenExponentField('exponent', '公钥指数').setOptional())
		.addField(new Field('n'))
		.addField(new Field('e'))
		.addField(new Field('d'))
		.addField(new Field('p'))
		.addField(new Field('q'))
		.addField(new Field('dp'))
		.addField(new Field('dq'))
		.addField(new Field('qi'))
		.withErrorTable({});

function generateKey() {
	if (!form.validate()) return;
	subtle.generateKey({
		name: 'RSA-PSS',
		modulusLength: Number(form.values.length.trim() || 3072),
		publicExponent: Buffer.fromBigInt(BigInt(form.values.exponent.trim() || 65537n)).asUint8Array(),
		hash: 'SHA-256'
	}, true, ['sign', 'verify']).then(
		({ privateKey }) => subtle.exportKey('jwk', privateKey)
	).then(exported => {
		(['n', 'e', 'd', 'p', 'q', 'dp', 'dq', 'qi'] as const).forEach(k => {
			form.values[k] = Buffer.fromBase64Url(exported[k]!).asBigInt().toString();
		});
	}, (err: unknown) => {
		form.setExtraError('', [
			'生成密钥失败，请更改参数或使用离线脚本。错误信息：',
			...err2lines(err),
		]);
	}).then(form.rerender.bind(form));
}

function downloadKey() {
	if (!form.values.n.length)
		return;
	plainDownload([
		`modulus (n): ${form.values.n}\n`,
		`public exponent (e): ${form.values.e}\n`,
		`private exponent (d): ${form.values.d}\n`,
		`prime 1 (p): ${form.values.p}\n`,
		`prime 2 (q): ${form.values.q}\n`,
		`private exponent (mod (prime 1 - 1)) (dp): ${form.values.dp}\n`,
		`private exponent (mod (prime 2 - 1)) (dq): ${form.values.dq}\n`,
		`(prime 2)^(-1) (mod prime 1) (qi): ${form.values.qi}\n`
	], 'key');
}

const genkey: React.FC = () => {
	useStore(form);
	useEffect(form.errorScroll.bind(form));

	return (
		<>
			<Header
				block
				as="h3"
				content="在线密钥生成"
				attached="top"
			/>
			<Segment attached="bottom" className={form.errorClass()}>
				<Form.Group>
					<Text form={form} label="密钥长度 (模数位数，范围: 512 ~ 4096)" name="length" placeholder="3072" width={6} />
					<Text form={form} label="公钥指数 (3 或 65537)" name="exponent" placeholder="65537" width={6} />
					<Form.Field width={4}>
						<label className="padding-label" />
						<Form.Group unstackable>
							<Form.Button color="pink" content="生成" icon="send" onClick={generateKey} />
							<Form.Button className="no-left-padding" primary content="下载" icon="download" onClick={downloadKey} />
						</Form.Group>
					</Form.Field>
				</Form.Group>
				<FormErrorMessage form={form} />
				<Divider />
				<TextArea form={form} readOnly small label={<>模数 <$ math="n" /></>} name="n" />
				<TextInput form={form} readOnly label={<>公钥指数 <$ math="e" /></>} name="e" icon="id key" iconPosition="left" />
				<TextArea form={form} readOnly small label={<>私钥指数 <$ math="d" /></>} name="d" />
				<Message warning size="small" style={{ display: 'block' }}>
					以下字段为 <a href="/login"><strong>CRT 优化签名函数</strong></a>部分所需的字段。
				</Message>
				<TextArea form={form} readOnly tiny label={<>较大素因子 <$ math="p_1" /> (p)</>} name="p" />
				<TextArea form={form} readOnly tiny label={<>较小素因子 <$ math="p_2" /> (q)</>} name="q" />
				<TextArea form={form} readOnly tiny label={<><$ math="d \bmod (p_1 - 1)" /> (dp)</>} name="dp" />
				<TextArea form={form} readOnly tiny label={<><$ math="d \bmod (p_2 - 1)" /> (dq)</>} name="dq" />
				<TextArea form={form} readOnly tiny label={<><$ math="p_2^{-1} \pmod {p_1}" /> (qi)</>} name="qi" />
			</Segment>
		</>
	);
}

genkey.displayName = 'genkey';

export default genkey;
