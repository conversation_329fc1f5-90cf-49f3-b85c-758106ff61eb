import { createElement, Fragment, useEffect } from 'react';
import { InlineMath as $ } from 'react-katex';
import { Form, Header, Icon, Message, Segment } from 'semantic-ui-react';
import { useStore } from 'zustand';

import TextArea from '../modules/form/components/TextArea';
import TextInput from '../modules/form/components/TextInput';
import ExponentField from '../modules/form/field/ExponentField';
import ModulusField from '../modules/form/field/ModulusField';
import UIDField from '../modules/form/field/UIDField';
import BaseForm from '../modules/form/form';

const form = new BaseForm()
	.addField(new UIDField('uid', 'UID'))
	.addField(new ModulusField('modulus', '模数'))
	.addField(new ExponentField('exponent', '公钥指数'));

const change: React.FC = () => {
	useStore(form);
	useEffect(form.errorScroll.bind(form));

	return (
		<>
			<Header
				block
				as="h3"
				content="用户信息"
				attached="top"
			/>
			<Segment attached="bottom" className="form">
				<Form.Group className="half-width-inline" style={{ alignItems: 'center' }}>
					<TextInput form={form} readOnly inline label="UID (暂时无法修改)" name="uid" icon="id card" iconPosition="left" placeholder="例: root" maxLength={20} style={{ flex: 1 }} />
				</Form.Group>
				<Header as="h4" dividing content="身份验证信息" className="size-16-5" />
				<TextArea form={form} small label={<>新模数 <$ math="n'" /></>} name="modulus" placeholder="例: 998244353" />
				<TextInput form={form} label={<>新指数 <$ math="e'" /></>} name="exponent" icon="key" iconPosition="left" placeholder="例: 65537" />
				<Header as="h4" dividing content="绑定验证信息" className="size-16-5" />
				<Message warning size="small" style={{ display: 'block' }}>
					<Message.List>
						<Message.Item>同一主机名的邮箱至多可绑定一个。</Message.Item>
						<Message.Item>对于一个主机名，验证码有效期为 10 分钟，发送间隔至少为 5 分钟。不同主机名的邮箱之间发送间隔至少为 15 秒。</Message.Item>
					</Message.List>
				</Message>
				<Header as="h4" dividing content="Ajax libs CDN 配置" className="size-16-5" />
				<Message negative size="small" icon>
					<Icon name="warning sign" style={{ fontSize: '2rem' }} />
					<Message.Content>
						该部分包含一些高级配置，如果你不知道你在干什么，则不应该修改它。
					</Message.Content>
				</Message>
			</Segment>
		</>
	);
}

change.displayName = 'change';

export default change;
