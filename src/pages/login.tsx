import { createElement, Fragment, MouseEvent, useEffect, useRef, useState } from 'react';
import { InlineMath as $ } from 'react-katex';
import { Button, Divider, Form, Grid, Header, Message, Segment, Tab, TabProps } from 'semantic-ui-react';
import { useStore } from 'zustand';

import Editor from '../components/editor/Editor';
import TextArea from '../modules/form/components/TextArea';
import TextInput from '../modules/form/components/TextInput';
import ExponentField from '../modules/form/field/ExponentField';
import Field from '../modules/form/field/Field';
import ModulusField from '../modules/form/field/ModulusField';
import UIDField from '../modules/form/field/UIDField';
import BaseForm from '../modules/form/form';
import BigIntRule from '../modules/form/rule/BigIntRule';
import { UserCenter } from '../user';

const simpleSignFuncGen = (d: bigint) => `\
const { PowerMod } = await import('src/util/nt');
const { UserCenter } = await import('src/user');
const user = UserCenter.getState();
const n = user.modulus.asBigInt();
const d = ${d}n;

return x => PowerMod(x, d, n); // ~ 35 ms
`, crtSignFuncGen = (p: bigint, q: bigint, dp: bigint, dq: bigint, qi: bigint) => `\
const { CRTRSA } = await import('src/util/nt');
const { UserCenter } = await import('src/user');
const user = UserCenter.getState();
const
	p = ${p}n,
	q = ${q}n,
	dp = ${dp}n,
	dq = ${dq}n,
	qi = ${qi}n;

return x => CRTRSA(x, p, q, dp, dq, qi); // ~ 10 ms
`;

const form = new BaseForm()
	.addField(new UIDField('uid', 'UID'))
	.addField(new ModulusField('modulus', '模数'))
	.addField(new ExponentField('exponent', '公钥指数'));

const simpleSignForm = new BaseForm()
	.addField(new Field('d').addRule(new BigIntRule(
		1n, undefined, () => "私钥指数必须为正整数"
	)));

const crtSignForm = new BaseForm()
	.addField(new Field('p').addRule(new BigIntRule(
		1n, undefined, () => "较大素因子必须为正整数"
	)))
	.addField(new Field('q').addRule(new BigIntRule(
		1n, undefined, () => "较小素因子必须为正整数"
	)))
	.addField(new Field('dp').addRule(new BigIntRule(
		1n, undefined, () => <><$ math="d \bmod (p_1 - 1)" /> 必须为正整数</>
	)))
	.addField(new Field('dq').addRule(new BigIntRule(
		1n, undefined, () => <><$ math="d \bmod (p_2 - 1)" /> 必须为正整数</>
	)))
	.addField(new Field('qi').addRule(new BigIntRule(
		1n, undefined, () => <><$ math="p_2^{-1} \pmod {p_1}" /> 必须为正整数</>
	)));

const login: React.FC = () => {
	const user = UserCenter();
	useStore(form);
	useStore(simpleSignForm);
	useStore(crtSignForm);
	useEffect(form.errorScroll.bind(form));

	const initial = localStorage.getItem('signFunc') ?? '';
	const setValueRef = useRef<(newValue: string) => void>(() => {/* initial */ });
	const layoutRef = useRef(() => {/* initial */ });

	const [signTabActiveIndex, setSignTabActiveIndex] = useState(0);

	const handleTabChange = (_: MouseEvent, { activeIndex }: TabProps) => {
		queueMicrotask(layoutRef.current);
		setSignTabActiveIndex(activeIndex as number);
	};

	const handleChange = (newValue: string) => {
		newValue ? localStorage.setItem('signFunc', newValue) : localStorage.removeItem('signFunc');
		setSignTabActiveIndex(2);
	};

	const handleSimple = () => {
		if (!simpleSignForm.validate()) return;
		const signFunc = simpleSignFuncGen(BigInt(simpleSignForm.values.d));
		setValueRef.current(signFunc);
		localStorage.setItem('signFunc', signFunc);
	};

	const handleCRT = () => {
		if (!crtSignForm.validate()) return;
		const
			p = BigInt(crtSignForm.values.p),
			q = BigInt(crtSignForm.values.q),
			dp = BigInt(crtSignForm.values.dp),
			dq = BigInt(crtSignForm.values.dq),
			qi = BigInt(crtSignForm.values.qi),
			signFunc = crtSignFuncGen(p, q, dp, dq, qi);
		setValueRef.current(signFunc);
		localStorage.setItem('signFunc', signFunc);
	};



	const
		SimpleSignForm = (
			<Tab.Pane attached={false} className="form">
				<Message color="pink" size="small">
					在下面的例子中，假设 <$ math="n = 998244353, e = 65537" />。
				</Message>
				<TextArea form={simpleSignForm} small label={<>私钥指数 <$ math="d" /></>} name="d" placeholder="例: 863961089" />
				<Button primary content="生成签名函数" icon="send" onClick={handleSimple} />
			</Tab.Pane>
		),
		CRTSignForm = (
			<Tab.Pane attached={false} className="form">
				<Message color="pink" size="small">
					在下面的例子中，假设 <$ math="p_1 = 100003, p_2 = 99991, e = 65537" />。
				</Message>
				<TextArea form={crtSignForm} tiny label={<>较大素因子 <$ math="p_1" /> (p)</>} name="p" placeholder="例: 100003" />
				<TextArea form={crtSignForm} tiny label={<>较小素因子 <$ math="p_2" /> (q)</>} name="q" placeholder="例: 99991" />
				<TextArea form={crtSignForm} tiny label={<><$ math="d \bmod (p_1 - 1)" /> (dp)</>} name="dp" placeholder="例: 7103" />
				<TextArea form={crtSignForm} tiny label={<><$ math="d \bmod (p_2 - 1)" /> (dq)</>} name="dq" placeholder="例: 72863" />
				<TextArea form={crtSignForm} tiny label={<><$ math="p_2^{-1} \pmod {p_1}" /> (qi)</>} name="qi" placeholder="例: 58335" />
				<Button primary content="生成签名函数" icon="send" onClick={handleCRT} />
			</Tab.Pane>
		),
		CustomSignForm = (
			<Tab.Pane attached={false}>
				<p>在左侧代码框中编辑代码。</p>
				<p>本功能适用于一些有特殊需求的用户，如同时登录多个账号 (通常可以通过判断 UID 来区分解密函数)。</p>
				<p>代码使用 JavaScript 语言，你需要返回 (<code style={{ color: 'blue' }}>return</code>) 一个函数，签名如下：<code>
					<span style={{ color: 'blue' }}>function</span> sign(): (data: <span style={{ color: 'blue' }}>bigint</span>) =&gt; <span style={{ color: 'blue' }}>bigint</span>
				</code>。</p>
				<p>你需要满足，设 <$ math="\operatorname{sign}(M) = S" />，则 <$ math="S^e \equiv M \pmod n" />。</p>
				<Divider />
				{/* <input type="file" id="upload-sign-script" style="display: none" /> */}
				<p>或者，你可以从本地文件上传：<Button primary content="选择文件" icon="upload" /></p>
			</Tab.Pane>
		);

	return (
		<>
			<Header
				block
				as="h3"
				content="登录信息配置"
				attached="top"
			/>
			<Segment attached className={form.errorClass()}>
				<Form.Group className="half-width-inline" style={{ alignItems: 'center' }}>
					<TextInput form={form} inline label="UID (留空以登出)" name="uid" icon="id card" iconPosition="left" placeholder="例: root" maxLength={20} style={{ flex: 1 }} />
					<Button color="pink" content="确认" onClick={() => { }} />
				</Form.Group>
				<TextArea form={form} readOnly small label={<>模数 <$ math="n" /> (自动填充)</>} name="modulus" />
				<Form.Group className="half-width-inline" >
					<TextInput form={form} readOnly inline label={<>指数 <$ math="e" /> (自动填充)</>} name="exponent" icon="key" iconPosition="left" style={{ flex: 1 }} />
				</Form.Group>
			</Segment>
			<Header
				block
				as="h4"
				content="签名函数配置区"
				attached
			/>
			<Segment attached="bottom">
				<Message success size="small">
					该部分和私钥相关，用于配置签名 (解密) 函数。
					<strong>代码保证在该部分进行的任何操作都不会和服务器进行任何交互 (即纯离线)</strong>。
				</Message>
				<Grid stackable>
					<Grid.Column width={8} style={{ paddingRight: '.5rem' }}>
						<Editor
							language="javascript"
							onChange={handleChange}
							value={initial}
							setValueRef={setValueRef}
							layoutRef={layoutRef}
						/>
					</Grid.Column>
					<Grid.Column width={8} style={{ paddingLeft: '.5rem' }}>
						<Header as="h4" dividing content="自动配置" className="size-16-5" />
						<Tab
							activeIndex={signTabActiveIndex}
							menu={{ stackable: true, widths: 3 }}
							onTabChange={handleTabChange}
							panes={[
								{ menuItem: '简单私钥签名函数', pane: SimpleSignForm },
								{ menuItem: 'CRT 优化签名函数', pane: CRTSignForm },
								{ menuItem: '自定义签名函数', pane: CustomSignForm },
							]}
							renderActiveOnly={false}
						/>
						<Button color="green" content="检验签名正确性" icon="clipboard check" onClick={() => { }} />
						<Message info size="small">
							丢失私钥？<a href="/resetkey">点此重置密钥</a>。
						</Message>
					</Grid.Column>
				</Grid>
			</Segment>
		</>
	);
}

login.displayName = 'login';

export default login;
