import { createElement, Fragment, useEffect } from 'react';
import { InlineMath as $ } from 'react-katex';
import { Button, Form, Header, Message, Segment } from 'semantic-ui-react';
import { useStore } from 'zustand';

import FormErrorMessage from '../modules/form/components/FormErrorMessage';
import TextArea from '../modules/form/components/TextArea';
import TextInput from '../modules/form/components/TextInput';
import EmailField from '../modules/form/field/EmailField';
import ExponentField from '../modules/form/field/ExponentField';
import ModulusField from '../modules/form/field/ModulusField';
import UIDField from '../modules/form/field/UIDField';
import VerifyCodeField from '../modules/form/field/VerifyCodeField';
import BaseForm from '../modules/form/form';
/*

	static errTable = {
		...super.errTable,
		uid: {
			...super.errTable.uid,
			notExist: uid => `UID ${uid} 不存在`
		},
		email: {
			notFound: (uid, email) => `用户 ${uid} 不存在<strong>非公开邮箱</strong> ${email}`
		},
		'email-verify': {
			invalid: t => `邮箱 ${t} 的验证码无效`,
			tooFrequently: t => `验证码发送过于频繁，请等待 ${t} 秒后再次重试`
		}
	}
*/
const form = new BaseForm()
	.addField(new UIDField('uid', 'UID'))
	.addField(new ModulusField('modulus', '模数'))
	.addField(new ExponentField('exponent', '公钥指数'))
	.addField(new EmailField('email', '邮箱'))
	.addField(new VerifyCodeField('email-verify', '邮箱验证码'));

const resetkey: React.FC = () => {
	useStore(form);
	useEffect(form.errorScroll.bind(form));

	return (
		<>
			<Header
				block
				as="h3"
				content="重置密钥"
				attached="top"
			/>
			<Segment attached="bottom" className="form">
				<TextInput form={form} label="UID" name="uid" icon="id card" iconPosition="left" placeholder="例: root" maxLength={20} />
				<Form.Group>
					<TextInput form={form} label="邮箱 (需非公开)" name="email" icon="mail" iconPosition="left" placeholder="例: <EMAIL>" maxLength={254} width={7} />
					<TextInput
						form={form}
						label="邮箱验证码"
						name="email-verify"
						icon="clipboard check"
						iconPosition="left"
						placeholder="24 位 Base64 串"
						spellCheck={false}
						maxLength={24}
						width={9}
						action={{
							content: '发送验证码',
							icon: 'send',
						}}
					/>
				</Form.Group>
				<Message warning size="small" style={{ display: 'block' }}>
					<Message.List>
						<Message.Item>验证码有效期为 10 分钟，相邻两次邮件的发送间隔至少为 5 分钟。</Message.Item>
						<Message.Item>重置密钥时的邮箱<strong>必须</strong>用户信息中<strong>非公开</strong>的邮箱。如果不满足条件，则该邮件不会发送 (即操作失败)，<strong>但仍计入 5 分钟冷却时间</strong>。</Message.Item>
					</Message.List>
				</Message>
				<TextArea form={form} small label={<>新模数 <$ math="n'" /></>} name="modulus" placeholder="例: 998244353" />
				<TextInput form={form} label={<>新指数 <$ math="e'" /></>} name="exponent" icon="key" iconPosition="left" placeholder="例: 65537" />
				<Button color="pink" content="提交" icon="send" onClick={() => { }} />
				<FormErrorMessage form={form} />
			</Segment>
		</>
	);
}

resetkey.displayName = 'resetkey';

export default resetkey;
