import { createElement, Fragment, useEffect, useState } from 'react';
import { Header, Segment } from 'semantic-ui-react';

import { Context, StandingsTable } from '../components/leaderboard/StandingsTable';
import SubmitForm from '../components/leaderboard/SubmitForm';
import ErrorMessage from '../components/util/ErrorMessage';
import type { LeaderBoardEntry } from '../models/leaderboard/leaderboard';
import type { SubmitData } from '../models/leaderboard/submitdata';
import { getLeaderBoard } from '../modules/leaderboard/api/leaderboard';
import { submit as submitApi } from '../modules/leaderboard/api/submit';
import { vote as voteApi } from '../modules/leaderboard/api/vote';

const LeaderBoard: React.FC = () => {
	const [loaded, setLoaded] = useState(false);
	const [leaderBoardData, setLeaderBoardData] = useState<LeaderBoardEntry[]>([]);
	const [error, setError] = useState<unknown>(null);

	useEffect(() => {
		(async () => {
			try {
				const data = await getLeaderBoard();
				setLoaded(true);
				setLeaderBoardData(data);
			} catch (e) {
				setError(e);
			}
		})();
	}, []);

	async function vote(user: string) {
		setLoaded(false);
		setError(null);
		try {
			const data = await voteApi(user);
			setLoaded(true);
			setLeaderBoardData(data);
		} catch (e) {
			setError(e);
		}
	}

	async function submit(data: SubmitData) {
		const board = await submitApi(data);
		setLeaderBoardData(board);
	}

	return (
		<>
			<Header
				block
				as="h3"
				content="SAST 2022 PyTorch Homework Leaderboard"
				attached="top"
			/>
			<Segment attached="bottom">
				<Context.Provider value={{ vote, submit }}>
					<Header as="h3" dividing content="排行榜" />
					{error
						? <ErrorMessage err={error} />
						: <StandingsTable loaded={loaded} data={leaderBoardData} />
					}
					<Header as="h3" dividing content="提交" />
					<SubmitForm />
				</Context.Provider>
			</Segment>
		</>
	)
}

LeaderBoard.displayName = 'LeaderBoard';

export default LeaderBoard;
