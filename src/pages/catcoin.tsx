import assert from 'nanoassert';
import { createElement, Fragment, useRef, useState } from 'react';
import { Button, Form, Header, Message, Segment, Table } from 'semantic-ui-react';

import { create, StoreApi, UseBoundStore } from 'zustand';
import { BlockModal } from '../components/catcoin/BlockModal';
import { Connection } from '../modules/catcoin/api';
import { Block } from '../modules/catcoin/block';
import { shortenHash } from '../modules/catcoin/util';
import { date2str } from '../util/date';

const BlocksCenter: UseBoundStore<StoreApi<Block[]>> = create(
	(set, get, api) => {
		assert(api.getState === get);
		assert(api.setState === set);
		return [];
	}
);

const catcoin: React.FC = () => {
	const [backendUrl, setBackendUrl] = useState('');
	const conn = useRef<Connection>(new Connection(BlocksCenter));
	const blocks = BlocksCenter();

	const handleInputChange = ({ target }: { target: { value: string } }) => {
		setBackendUrl(target.value);
	}

	const handleConnect = () => {
		conn.current.assign(backendUrl);
	}

	const renderedBlocks = blocks.slice().reverse().map(block => (
		<BlockModal block={block} key={block.hash.asHex()}>
			<Table.Row>
				<Table.Cell content={block.height} />
				<Table.Cell content={shortenHash(block.hash.asHex())} />
				<Table.Cell content={date2str(block.time)} />
				<Table.Cell content={block.getDifficulty()} />
				<Table.Cell content={block.txns.length ? block.txns.length - 1 : '/'} />
				<Table.Cell content={<samp>0x{block.nonce.toString(16).padStart(16, '0')}</samp>} />
				<Table.Cell content={`${block.raw.length} B`} />
				<Table.Cell content={isNaN(block.reward) ? '/' : `${block.reward} CTC`} />
			</Table.Row>
		</BlockModal>
	)).filter(block => block);

	return (
		<>
			<Header
				block
				as="h3"
				content="Catcoin"
				attached="top"
			/>
			<Segment attached="bottom">
				<Message info size="small">
					<p>提示：使用 <code>ssh -NTL &lt;<em>port1</em>&gt;:localhost:&lt;<em>port2</em>&gt; os</code> 将服务器 <em>port2</em> 端口的服务转发到本机上的 <em>port1</em> 端口，然后在下方 “后端 URL” 处填入 <code>localhost:&lt;<em>port1</em>&gt;</code>。</p>
				</Message>
				<Form>
					<Form.Group className="half-width-inline" style={{ alignItems: 'center' }}>
						<Form.Input
							icon="linkify"
							iconPosition="left"
							inline
							label="后端 URL"
							name="backend-url"
							onChange={handleInputChange}
							placeholder="localhost:1832"
							style={{ flex: 1 }}
							value={backendUrl}
						/>
						<Button color="pink" content="连接" onClick={handleConnect} />
					</Form.Group>
				</Form>
				<Table textAlign="center" celled compact selectable unstackable>
					<Table.Header>
						<Table.Row>
							<Table.HeaderCell content="#" />
							<Table.HeaderCell content="Hash" />
							<Table.HeaderCell content="时间" />
							<Table.HeaderCell content="难度" />
							<Table.HeaderCell content="交易数" />
							<Table.HeaderCell content="Nonce" />
							<Table.HeaderCell content="块大小" />
							<Table.HeaderCell content="奖励金额" />
						</Table.Row>
					</Table.Header>
					<Table.Body>
						{renderedBlocks}
					</Table.Body>
				</Table>
			</Segment>
		</>
	);
}

catcoin.displayName = 'catcoin';

export default catcoin;
