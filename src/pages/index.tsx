import { createElement, Fragment } from 'react';
import { Header, Message, Segment } from 'semantic-ui-react';

const index: React.FC = () => {
	return (
		<>
			<Header
				block
				as="h3"
				content="首页"
				attached="top"
			/>
			<Segment attached="bottom">
				<p>🚧 建设中 ... 🚧 (<a href="/static/cv.pdf">Curriculum Vitae</a>)</p>
				<Message info size="small">
					<p>建议使用最新版 Chrome 浏览器 (或 Edge 等 <strong style={{ color: 'fuchsia' }}>Chromium 内核</strong>浏览器) 访问本站，否则出现问题概不负责 (不兼容)。</p>
					<p style={{ color: 'fuchsia', fontSize: '1rem', fontWeight: 'bolder' }}>提示：注册时的指数和模数为 RSA 登录凭证，请大家注册时模数不要过小，以防暴力破解！</p>
					<p>提示：网站 React 重构已完成。</p>
				</Message>
				<p><a href="/我的少女时代">我的少女时代 - 全剧本同步</a></p>
				<p><a href="/turnabout-heardle">Ace Attorney Heardle</a></p>
				<p><a href="/pi-base">π-Base 实验性镜像</a></p>
				<p><a href="/lean">Lean4 游戏服务器实验性镜像</a></p>
				<p><a href="/factordb">无污染 FactorDB 镜像</a></p>
				<p><a href="/float-toy">Float Toy</a></p>
				<p><a href="/sass">SASS 指令集手册</a></p>
			</Segment>
		</>
	);
}

index.displayName = 'index';

export default index;
