import { createElement, Fragment, useEffect, useMemo, useRef, useState } from 'react';
import { Accordion, AccordionPanelProps, AccordionTitleProps, Grid, Header, Segment } from 'semantic-ui-react';

interface Paragraph {
	title: string;
	lines: string[];
	timeStamp: number;
}

function t2s(t: number) {
	const h = Math.floor(t / 3600), m = Math.floor(t / 60) % 60, s = Math.floor(t) % 60;
	return `${h ? `${h}:${m.toString().padStart(2, '0')}` : `${m}`}:${s.toString().padStart(2, '0')}`;
}

const ourTimes: React.FC = () => {
	const [activeIndex, setActiveIndex] = useState(-1);
	const [currentTime, setCurrentTime] = useState(0);
	const [data, setData] = useState<Paragraph[]>([]);
	const [nonce, setNonce] = useState(0);

	useEffect(() => {
		fetch('我的少女时代.md').then(x => x.text()).then(text => {
			const parse = (t: string) => {
				let r = 0;
				for (const s of t.split(':')) r = r * 60 + Number(s);
				return r;
			}
			const data = [], regex = /^\s*\[([^\]]+)\]\s*(.+)$/;
			let current: Paragraph = { title: '', lines: [], timeStamp: 0 };
			for (const line of text.split('\n\n')) {
				let mat: RegExpMatchArray | null;
				const
					isHeader = line.startsWith('### ') && (mat = line.substring(4).match(regex)) !== null,
					isEnd = line.startsWith('-END-');
				if ((isHeader || isEnd) && current.lines.length)
					data.push(current);
				if (isHeader) {
					current = { title: mat![2], lines: [], timeStamp: parse(mat![1]) };
				} else if (isEnd) {
					break;
				} else {
					current.lines.push(line);
				}
			}
			setData(data);
		});
	}, []);

	const
		vCurrent = useRef<HTMLElement | null>(null),
		active = (x => (x < 0 ? data.length : x) - 1)(data.findIndex(paragraph => paragraph.timeStamp > currentTime));

	useEffect(() => {
		setActiveIndex(active);
		setNonce(nonce => nonce + 1);
	}, [active]);

	useEffect(() => {
		vCurrent.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
	}, [nonce]);

	const panels = useMemo(() => data.map<AccordionPanelProps>((paragraph, idx) => ({
		key: idx,
		title: {
			className: idx === active ? 'v-active' : null,
			content: <>
				<span>{paragraph.title}</span>
				<span className="prompt" style={{ float: 'right' }}>{t2s(paragraph.timeStamp)}</span>
			</>,
			ref: (ref: HTMLElement) => {
				if (idx === active) {
					vCurrent.current = ref;
				}
			},
		},
		content: {
			className: idx === active ? 'v-active' : null,
			content: paragraph.lines.map((line, idx) => <p key={idx}>{line}</p>),
		},
	})), [data, active]);

	const video = useRef<HTMLVideoElement>(null);

	const handleTitleClick = (e: React.MouseEvent<HTMLDivElement>, itemProps: AccordionTitleProps) => {
		const index = Number(itemProps.index);
		setActiveIndex(index === activeIndex ? -1 : index);
		if ((e.ctrlKey || e.metaKey) && video.current) {
			const t = data[index].timeStamp;
			video.current.currentTime = t;
			setCurrentTime(t);
		}
	};

	const handleTimeUpdate = (e: React.SyntheticEvent<HTMLVideoElement>) => {
		setCurrentTime(e.currentTarget.currentTime);
	};

	return (
		<>
			<Header
				block
				as="h3"
				content="我的少女时代"
				attached="top"
			/>
			<Segment attached="bottom" className="form">
				<Grid stackable>
					<Grid.Column width={10} style={{ paddingRight: '.5rem' }}>
						<video
							controls={true}
							src="我的少女时代.mp4"
							style={{ maxWidth: '100%' }}
							onTimeUpdate={handleTimeUpdate}
							ref={video}
						/>
					</Grid.Column>
					<Grid.Column width={6} style={{ paddingLeft: '.5rem' }}>
						<div style={{ maxHeight: 'calc(100vh - 168px) ', overflowY: 'auto', padding: 1 }}>
							<Accordion
								activeIndex={activeIndex}
								fluid
								panels={panels}
								styled
								onTitleClick={handleTitleClick}
							/>
						</div>
					</Grid.Column>
				</Grid>
			</Segment>
		</>
	);
}

ourTimes.displayName = '我的少女时代';

export default ourTimes;
