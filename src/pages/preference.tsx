import assert from 'nanoassert';
import { createElement, Fragment, useEffect, useMemo, useRef, useState } from 'react';
import { <PERSON><PERSON>, Grid, Header, Message, Segment } from 'semantic-ui-react';
import { Manager } from 'socket.io-client';

import { Buffer, FixedLengthBuffer } from '../buffer';
import Editor from '../components/editor/Editor';
import type { Config } from '../models/config';
import User, { UserCenter } from '../user';
import { ConfigCenter, ConfigWithTime } from '../util/config';
import { deAES, enAES, EncryptedBundle } from '../util/crypto';
import { date2str } from '../util/date';
import { safeParse, sortedStringify } from '../util/json';
import { checkIntRange } from '../util/nt';
import { socket } from '../util/socket';

function encodeRaw(config: ConfigWithTime): Uint8Array {
	const config_str = sortedStringify(config.config);
	const buf = Buffer.fromUtf8('\0'.repeat(8) + config_str).asUint8Array();
	const view = new DataView(buf.buffer, buf.byteOffset, buf.byteLength);
	view.setBigUint64(0, BigInt(config.lastModified.getTime()), true);
	return buf;
}

function decodeRaw(buf: Uint8Array): ConfigWithTime {
	const view = new DataView(buf.buffer, buf.byteOffset, buf.byteLength);
	const lastModified = new Date(Number(view.getBigUint64(0, true)));
	const config = safeParse(Buffer.fromUint8Array(buf.subarray(8)).asUtf8())[0] as Config;
	return new ConfigWithTime(config, lastModified);
}

async function encode(config: ConfigWithTime, user: User): Promise<Uint8Array> {
	const { key, data: { ivKey, data } } = await enAES(Buffer.fromUint8Array(encodeRaw(config)), user);

	const key_u8 = key.asUint8Array();
	const ivKey_u8 = ivKey.asUint8Array();
	const data_u8 = data.asUint8Array();
	assert(checkIntRange(key_u8.length, 0, 65535), 'The length of key is too long');
	assert(ivKey_u8.length === 48, 'The length of (IV, key) should be 48');

	const buf = new ArrayBuffer(key_u8.length + data_u8.length + 50);

	const u8_view = new Uint8Array(buf);
	new DataView(buf).setUint16(0, key_u8.length, true);
	u8_view.set(key_u8, 2);
	u8_view.set(ivKey_u8, key_u8.length + 2);
	u8_view.set(data_u8, key_u8.length + 50);

	return u8_view;
}

async function decode(encrypted: Uint8Array, user: User): Promise<ConfigWithTime> {
	const view = new DataView(encrypted.buffer, encrypted.byteOffset, encrypted.byteLength);
	const key_length = view.getUint16(0, true);
	const key = Buffer.fromUint8Array(encrypted.subarray(2, key_length + 2));

	const ivKey = FixedLengthBuffer.fromUint8Array(encrypted.subarray(key_length + 2, key_length + 50));
	assert(ivKey.length === 48, 'The length of (IV, key) should be 48');

	const data = Buffer.fromUint8Array(encrypted.subarray(key_length + 50));

	const buffer = await deAES(new EncryptedBundle(key, { ivKey, data }), user);
	return decodeRaw(buffer.asUint8Array());
}

const preference: React.FC = () => {
	const user = UserCenter();
	const config = ConfigCenter();
	const initial = useMemo(() => sortedStringify(config.config, '\t'), []);
	const setValueRef = useRef<(newValue: string) => void>(() => {/* initial */ });

	const [remoteCwt, setRemoteCwt] = useState<unknown>(null);
	const value = useRef<string>(initial);
	const valid = useRef<boolean>(true);
	const conn = useRef<Manager | null>(null);
	{
		function send(conn: Manager | null, user: User | null) {
			if (conn && conn._readyState === 'open' && user && user.modulus) {
				user.sign(null).then(data => conn.engine.send(JSON.stringify(data)));
			}
		}

		useEffect(() => {
			function send_user(this: Manager) {
				send(this, UserCenter.getState());
			}

			function recv_data(data: string | ArrayBuffer) {
				if (typeof data !== 'string' && data[Symbol.toStringTag] === 'ArrayBuffer') {
					if (data.byteLength) {
						decode(new Uint8Array(data), UserCenter.getState()!).then(setRemoteCwt, setRemoteCwt);
					} else {
						setRemoteCwt(null);
					}
				}
			}

			const c = conn.current = socket(new URL('/preference-sync', location.origin));
			c.on('open', send_user);
			c.on('data', recv_data);
			return () => {
				c.off('open', send_user);
				c.off('data', recv_data);
			};
		}, []);

		useEffect(() => send(conn.current, user), [user]);
	}

	const notLoggedIn = !(user && user.modulus);

	const handleChange = (newValue: string) => {
		const [obj, flag] = safeParse(newValue) as [Config, boolean];
		if ((valid.current = flag)) {
			ConfigCenter.setState(new ConfigWithTime(obj, new Date()));
			value.current = sortedStringify(obj, '\t');
		}
	}

	if (valid.current) {
		const newValue = sortedStringify(config.config, '\t');
		if (newValue !== value.current) {
			setValueRef.current(value.current = newValue);
		}
	}


	let localTag, remoteTag;
	if (notLoggedIn) {
		localTag = <span style={{ color: '#0c0' }}>{date2str(config.lastModified)}</span>;
		remoteTag = <span style={{ color: 'red' }}>未登录</span>;
	} else if (remoteCwt instanceof ConfigWithTime) {
		localTag = <span style={{ color: config.lastModified < remoteCwt.lastModified ? 'red' : '#0c0' }}>{date2str(config.lastModified)}</span>;
		remoteTag = <span style={{ color: remoteCwt.lastModified < config.lastModified ? 'red' : '#0c0' }}>{date2str(remoteCwt.lastModified)}</span>;
	} else if (remoteCwt) {
		localTag = <span style={{ color: '#0c0' }}>{date2str(config.lastModified)}</span>;
		remoteTag = <span style={{ color: 'red' }} title={`可能的原因有：数据已损坏，身份认证未通过等。错误信息：${remoteCwt}`}>错误</span>;
	} else {
		localTag = <span style={{ color: '#0c0' }}>{date2str(config.lastModified)}</span>;
		remoteTag = <span style={{ color: 'red' }}>无记录</span>;
	}


	const
		tryUpload = () => {
			if (remoteCwt instanceof ConfigWithTime && config.lastModified < remoteCwt.lastModified) {
				if (confirm('test2') !== true)
					return;
			}
			if (user && user.modulus && conn.current && conn.current._readyState === 'open') {
				encode(config, user).then(payload => conn.current?.engine.send(payload));
			}
		},
		tryDownload = () => {
			if (!(remoteCwt instanceof ConfigWithTime)) return;
			if (!(config.lastModified <= remoteCwt.lastModified)) {
				if (confirm('test') !== true)
					return;
			}
			ConfigCenter.setState(remoteCwt);
			valid.current = true;
		},
		trySync = () => {
			if (remoteCwt instanceof ConfigWithTime && config.lastModified < remoteCwt.lastModified) { // download
				ConfigCenter.setState(remoteCwt);
				valid.current = true;
			} else if (remoteCwt instanceof ConfigWithTime && config.lastModified.getTime() === remoteCwt.lastModified.getTime()) { // nop
			} else if (user && user.modulus && conn.current && conn.current._readyState === 'open') { // upload
				encode(config, user).then(payload => conn.current?.engine.send(payload));
			}
		};

	return (
		<>
			<Header
				block
				as="h3"
				content="偏好设置"
				attached="top"
			/>
			<Segment attached="bottom">
				<Grid stackable>
					<Grid.Column width={8} style={{ paddingRight: '.5rem' }}>
						<Editor
							language="json"
							onChange={handleChange}
							value={initial}
							setValueRef={setValueRef}
						/>
					</Grid.Column>
					<Grid.Column width={8} style={{ paddingLeft: '.5rem' }}>
						<Header as="h4" dividing content="同步" className="size-16-5" />
						<Grid className="very-easily-stackable">
							<Grid.Column width={7} className="no-right-padding">
								<p>
									本地最后更新：{localTag}<br />
									远程最后更新：{remoteTag}
								</p>
							</Grid.Column>
							<Grid.Column width={9} className="very-easily-lose-padding-top">
								<Button.Group>
									<Button primary content="上传" icon="upload" disabled={notLoggedIn} onClick={tryUpload}></Button>
									<Button color="pink" content="自动同步" icon="sync" disabled={notLoggedIn} onClick={trySync}></Button>
									<Button primary content="下载" icon="download" disabled={notLoggedIn || !(remoteCwt instanceof ConfigWithTime)} onClick={tryDownload}></Button>
								</Button.Group>
							</Grid.Column>
						</Grid>
						<Message warning size="small">
							<p>同步信息时传输的是加密数据，因此更改密钥后同步数据可能会失效，请慎重考虑。</p>
							<p>“自动同步” 功能会比对本地最后更新时间和远程最后更新时间，从而智能选择上传或下载。</p>
						</Message>
					</Grid.Column>
				</Grid>
			</Segment>
		</>
	);
}

preference.displayName = 'preference';

export default preference;
