{
	"compilerOptions": {
		"esModuleInterop": true,
		"experimentalDecorators": true,
		"jsx": "react",
		"jsxFactory": "createElement",
		"jsxFragmentFactory": "Fragment",
		"lib": ["DOM", "DOM.Iterable", "ESNext"],
		"module": "esnext",
		"moduleResolution": "node",
		"paths": {
			"module-trie": ["./src/models/global"],
		},
		"resolveJsonModule": true,
		"rootDir": ".",
		"strict": true,
		"target": "esnext",
	},
	"include": ["src/**/*"],
}
