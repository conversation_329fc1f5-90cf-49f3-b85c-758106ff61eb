import { type Transport } from 'tone';

import ModalBase from '../modal/ModalBase.js';
import { dropDownBasicConfig } from '../render.js';
import { assert, checkIntRange } from '../util/type.js';
import { frontendConfig, setAndSaveConfig } from '../util/web.js';

interface ChordleSettingsConfig {
	onChange: (noteConvert: string) => void;
	latencyTest: () => void;
	latencyTestEnd: () => void;
	transport: typeof Transport
}

interface ChordleSettingsFields {
	noteConvert: JQuery;
	latency: JQuery<HTMLInputElement>;
	showHelper: JQuery<HTMLInputElement>;
	pageLength: JQuery<HTMLInputElement>;
	numberBins: JQuery<HTMLInputElement>;
	latencyTest: {
		button: JQuery;
		canvas: JQuery<HTMLCanvasElement>;
	}
}

export default class ChordleSettings extends ModalBase {
	#fields = <ChordleSettingsFields>{};
	#testing = false;
	settings: ChordleSettingsConfig;

	constructor(settings: ChordleSettingsConfig) {
		super('ui modal');
		this.settings = settings;
	}

	firstRender() {
		Object.assign(this.#fields, {
			noteConvert: $('<div class="ui fluid search selection dropdown">').append(
				$('<input type="hidden" name="noteConvert">'),
				$('<i class="dropdown icon">'),
				$('<div class="default text">')
			).dropdown({
				...dropDownBasicConfig,
				clearable: false,
				values: [
					{ name: '绝对音阶 / 固定调 (C, D, E, F, G, A, B)', value: 'absolute' },
					{ name: '相对音阶 / 首调 (1, 2, 3, 4, 5, 6, 7)', value: 'numbered' },
					{ name: '级数表示 (I, II, III, IV, V, VI, VII)', value: 'series' }
				],
				onChange: this.settings.onChange
			}),
			latency: $('<input type="text" name="latency" placeholder="0">'),
			showHelper: $('<input type="checkbox" name="showHelper">'),
			pageLength: $('<input type="text" name="pageLength" placeholder="50">'),
			numberBins: $('<input type="text" name="numberBins" placeholder="6">'),
			latencyTest: {
				button: $('<button class="ui icon circular pink button"><i class="play icon"></i></button>'),
				canvas: $('<canvas>'),
			}
		});

		this.#fields.latency.onInput(function (this: HTMLInputElement) {
			if (this.value === '') return setAndSaveConfig(['chordle', 'latency'], null);
			const latency = Number(this.value);
			if (0 <= latency && latency <= 60) setAndSaveConfig(['chordle', 'latency'], latency);
		});

		this.#fields.showHelper.on('input', function (this: HTMLInputElement) {
			assert(this.checked === true || this.checked === false);
			setAndSaveConfig(['chordle', 'disableHelper'], !this.checked);
		});

		this.#fields.pageLength.onInput(function (this: HTMLInputElement) {
			if (this.value === '') return setAndSaveConfig(['chordle', 'pageLength'], null);
			const pageLength = Number(this.value);
			if (checkIntRange(pageLength, 10, 500)) setAndSaveConfig(['chordle', 'pageLength'], pageLength);
		});

		this.#fields.numberBins.onInput(function (this: HTMLInputElement) {
			if (this.value === '') return setAndSaveConfig(['chordle', 'numberBins'], null);
			const numberBins = Number(this.value);
			if (checkIntRange(numberBins, 6, 30)) setAndSaveConfig(['chordle', 'numberBins'], numberBins);
		});

		this.$header!.text('设置');
		this.$content!.append(
			$('<div class="ui form">').append(
				$('<div class="ui no-shadow segment">').append(
					$('<h3>').text('延迟测试'),
					$('<p>').html('测试方法：点击下方的播放按钮，它会播放一段 BPM 为 240 的 4/4 拍的节拍器的音频 (含有<strong>强拍</strong>，<strong>次强拍</strong>和<strong>弱拍</strong>)，你需要在一个小节内分别按下键盘中的 <code>D</code>, <code>F</code>, <code>J</code>, <code>K</code> 键 (即标准键位)，重复多次即可测出音频输出延迟。'),
					$('<div class="latency-test">').append(
						this.#fields.latencyTest.button,
						this.#fields.latencyTest.canvas
					)
				),
				$('<div class="inline field">').append(
					$('<label>').text('音频输出延迟 (秒)'),
					this.#fields.latency
				),
				$('<div class="ui divider">'),
				$('<div class="two fields">').append(
					$('<div class="field">').append(
						$('<label>').text('音阶显示形式'),
						this.#fields.noteConvert
					),
					$('<div class="field">').css('textAlign', 'right').append(
						$('<label class="cspl">'),
						$('<div class="ui toggle checkbox">').append(
							this.#fields.showHelper,
							$('<label>').text('启动时显示帮助')
						)
					),
				),
				$('<div class="two fields">').append(
					$('<div class="field">').append(
						$('<label>').text('每页显示行数'),
						this.#fields.pageLength
					),
					$('<div class="field">').append(
						$('<label>').text('次数分布直方图组数'),
						this.#fields.numberBins
					)
				)
			)
		);

		const
			_1: { [key: string]: number; } = { KeyD: 0, KeyF: 1, KeyJ: 2, KeyK: 3 },
			{ transport } = this.settings, dpr = window.devicePixelRatio,
			{ latency: $latency } = this.#fields,
			{ canvas } = this.#fields.latencyTest, ctx = canvas[0].getContext('2d')!,
			rect = { left: 0, width: 0 }, samples: number[] = [];
		let oldWidth: number;
		function redraw(width: number) {
			oldWidth = width;
			canvas[0].width = width * dpr;
			canvas[0].height = 34 * dpr;
			ctx.font = '14px Lato';
			ctx.textAlign = 'center';
			ctx.scale(dpr, dpr);
			ctx.strokeStyle = 'rgba(34, 36, 38, .15)';
			ctx.fillStyle = 'gray';
			rect.left = (width > 300 ? 10 : 2);
			rect.width = width - 2 * rect.left;
			for (let i = 0; i <= 10; ++i) {
				const x = rect.left + rect.width * i / 10;
				if (width > 300) ctx.fillText((i / 10).toString(), x, 25);
				ctx.strokeRect(x, 0, 0, 10);
			}
		}

		function receiver(e: JQuery.KeyPressEvent) {
			if (!Object.hasOwn(_1, e.code)) return;
			const
				pos = _1[e.code], width = canvas.width()!,
				observe = transport.seconds, real = Math.floor(observe - pos * .25) + pos * .25,
				latency = observe - real;
			if (width !== oldWidth) redraw(width);

			const
				x = rect.left + rect.width * latency,
				hue = Math.random() * 360;
			ctx.strokeStyle = `hsl(${hue}, 100%, 50%)`;
			ctx.strokeRect(x, 0, 0, 35);

			// binary search
			let L = 0, R = samples.length, M; const n = R + 1;
			for (; L < R; M = (L + R) >> 1, samples[M] < latency ? L = M + 1 : R = M);
			samples.splice(L, 0, latency);

			// compute mean of quartiles (25% ~ 75%)
			let sum = n === 1 ? -samples[0] : 0; L = (n + 3) >> 2, R = n - L;
			for (let i = L; i < R; ++i) sum += samples[i];
			sum = (sum * 4 + (-n & 3) * (samples[L - 1] + samples[R])) / (2 * n);

			$latency.val(sum = Math.max(sum - .05, 0));
			setAndSaveConfig(['chordle', 'latency'], sum);
		}

		const
			_2 = <Element>this.#fields.latencyTest.button[0].firstChild,
			start = () => {
				assert(_2.classList.replace('play', 'stop'));
				$(document).on('keypress', receiver);
				redraw(canvas.width()!);
				samples.splice(0);
				this.settings.latencyTest();
			},
			end = () => {
				assert(_2.classList.replace('stop', 'play'));
				$(document).off('keypress', receiver);
				ctx.clearRect(0, 0, canvas[0].width / dpr + 1, 35);
				this.settings.latencyTestEnd();
			}

		this.#fields.latencyTest.button.on('click', () => ((this.#testing = !this.#testing)) ? start() : end());
		this.$element!.modal('setting', 'onHide', () => !this.#testing || (this.#testing = false, end()));
	}

	render() {
		const
			{ chordle: chordleConfig } = frontendConfig,
			noteConvert = ['numbered', 'series'].includes(chordleConfig?.noteConvert) ? chordleConfig.noteConvert : 'absolute';

		this.#fields.noteConvert.dropdown('set selected', noteConvert);

		if (!frontendConfig.chordle?.disableHelper)
			this.#fields.showHelper[0].checked = true;

		if (0 <= chordleConfig?.latency && chordleConfig?.latency <= 60)
			this.#fields.latency.val(chordleConfig.latency);

		if (checkIntRange(chordleConfig?.numberBins, 6, 30))
			this.#fields.numberBins.val(chordleConfig.numberBins);

		if (checkIntRange(chordleConfig?.pageLength, 10, 500))
			this.#fields.pageLength.val(chordleConfig.pageLength);
	}
}
