import type { Tonality } from '../models/chordle';
import { checkIntRange } from '../util/type.js';
import Chord from './chord.js';
import { parse as parseType } from './chordparser/dfa.js';

interface DiatonicScale extends Array<number> {
	major: number[];
	minor: number[];
}

interface CharMapper {
	[char: string]: number;
}

const
	diatonicScale = <DiatonicScale>[0, 2, 4, 5, 7, 9, 11, 0, 2, 4, 5, 7],
	functional: CharMapper = { '#': 1, 'x': 2, 'X': 2, '×': 2, '⌗': 1, '♭': -1, '♮': 0, '♯': 1, '✕': 2, '\udd2a': 2, '\udd2b': -2 },
	r2i: CharMapper = { 'I': 1, 'V': 5, 'i': 1, 'v': 5 },
	romans = 'ⅠⅡⅢⅣⅤⅥⅦ';

diatonicScale.major = diatonicScale;
diatonicScale.minor = [0, 2, 3, 5, 7, 8, 10];

export function parseChord(string: string, tonality: Tonality) {
	/*
		0: begin, 1: relative-numbered-end, 2: relative-series-end, 3: absolute-end
		4: reading-roman, 5: reading-roman-b, 6: 'b's
	*/
	let i, delta = 0, state = 0, root = 0, rootArray: number[] = [];
	root: for (i = 0; i <= string.length; ++i) {
		const ch = string[i], code = string.charCodeAt(i);
		if (55296 <= code && code <= 55552) continue;
		if (!(state && ch === '♭' && string[i + 1] === '5')) {
			if (Object.hasOwn(functional, ch)) { delta += functional[ch]; continue; }
		}
		const isBlank = /\s/.test(ch), isLetter = ('A' <= ch && ch <= 'G') || ('a' <= ch && ch <= 'g'), romanPos = romans.indexOf(ch);
		switch (state) {
			case 0: {
				if (isBlank) break;
				if (ch === 'b') { state = 6; root = -1; break; }
				if ('1' <= ch && ch <= '7') { state = 1; root = diatonicScale[code - 49]; break; }
				if (~romanPos) { state = 2; root = diatonicScale[romanPos]; break; }
				if (Object.hasOwn(r2i, ch)) { state = 4; rootArray = [r2i[ch]]; break; }
				if (isLetter) { state = 3; root = diatonicScale[(code & 31) + 4]; break; }
				break root;
			}
			case 1:
			case 2:
			case 3: {
				if (ch === 'b' && string[i + 1] !== '5') { --delta; break; }
				break root;
			}
			case 4:
			case 5: {
				if (Object.hasOwn(r2i, ch)) { rootArray.push(r2i[ch]); break; }
				const value = rootArray.reduce((x, y, i, a) => y < a[i + 1] ? x - y : x + y, 0);
				if (!checkIntRange(value, 1, 7)) { // backtracking
					i -= rootArray.length;
					state = (state == 5 ? 3 : 0);
					root = 0;
					break root;
				}
				state = 2;
				root = diatonicScale[tonality.scale][value - 1];
				if (ch === 'b' && string[i + 1] !== '5') { --delta; break; }
				break root;
			}
			case 6: {
				if (isBlank) { state = 3; break root; }
				if (ch === 'b') {
					// special treatment for bb5 (B♭5) chords.
					if (string[i + 1] === '5') { state = 3; break root; }
					else { --root; break; }
				}
				// special treatment for bdim7, bdom7, etc.
				if ('Dd'.includes(ch) && 'IOio'.includes(string[i + 1])) { state = 3; break root; }
				if ('1' <= ch && ch <= '7') { state = 1; root += diatonicScale[code - 49]; break; }
				if (~romanPos) { state = 2; root += diatonicScale[romanPos]; break; }
				if (Object.hasOwn(r2i, ch)) { state = 5; delta += root; rootArray = [r2i[ch]]; break; }
				if (isLetter) { state = 3; root += diatonicScale[(code & 31) + 4]; break; }
				state = 3;
				break root;
			}
		}
	}
	switch (state) {
		case 1: root += tonality.tonic + (tonality.scale === 'minor' ? 3 : 0); break;
		case 2: root += tonality.tonic; break;
		case 3: break;
		default: return null;
	}
	const [type, offset] = parseType(string.substring(i));
	root = (root + delta + offset) % 12;
	return Chord.fromCache(type, root < 0 ? root + 12 : root);
}
