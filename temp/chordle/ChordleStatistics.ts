import { Chart, BarController, CategoryScale, LinearScale, PointElement, BarElement, Title, Tooltip, Legend } from '/ajax/transit/chart';

import ModalBase from '../modal/ModalBase.js';
import type { ChordleMetadata, timeStat } from '../models/chordle';
import { chartBasicPlugin, chartBasicScale, chartBasicTooltip } from '../render.js';
import { range } from '../util/nt.js'
import { assert, checkIntRange, date2datestr } from '../util/type.js';
import { frontendConfig } from '../util/web.js';

Chart.register(
	BarController,
	CategoryScale,
	LinearScale,
	PointElement,
	BarElement,
	Title,
	Tooltip,
	Legend
);

export default class ChordleStatistics extends ModalBase {
	metadata: ChordleMetadata;
	#statistics: { [key: string]: JQuery } = {};

	constructor(metadata: ChordleMetadata) {
		super('ui modal');
		this.metadata = metadata;
	}

	#addStatisticElement(key: string, name: string, flip = false) {
		const
			$label = $('<div class="label">').text(name),
			$value = this.#statistics[key] = $('<div class="value">');
		return flip ? $('<div class="statistic">').append($label, $value) : $('<div class="statistic">').append($value, $label);
	}

	firstRender() {
		this.$header!.text('统计信息');
		const userInfo = this.metadata.loggedIn ? [
			$('<h3>').text('玩家信息'),
			$('<div class="ui four tiny statistics">').css({ marginTop: '-.5em', marginBottom: '-.5em' }).append(
				this.#addStatisticElement('userCountAll', '游戏次数 (in-game)'),
				this.#addStatisticElement('userCount', '获胜次数 (in-game)'),
				this.#addStatisticElement('winRate', '胜率 (in-game)'),
				this.#addStatisticElement('userAverage', '平均尝试 (in-game)')
			),
			$('<div class="ui four pink tiny statistics">').append(
				this.#addStatisticElement('userCountAllI', '解题次数 (off-game)'),
				this.#addStatisticElement('userCountI', '完成次数 (off-game)'),
				this.#addStatisticElement('winRateI', '完成率 (off-game)'),
				this.#addStatisticElement('userAverageI', '平均尝试 (off-game)')
			),
			$('<h3>').text('连胜信息'),
			$('<div class="ui two tiny statistics">').append(
				this.#addStatisticElement('currentStreak', '当前连胜'),
				this.#addStatisticElement('maxStreak', '最大连胜'),
			),
			$('<h3>').text('次数分布').css('margin-bottom', 0),
			this.#statistics.distCanvas = $('<canvas>').css('maxHeight', 200)
		] : [
			$('<div class="ui small error visitor message">').html('您现在处于未登录状态，请<a href="/login">登录</a>以查看完整的统计信息。')
		];
		this.$content!.append(
			$('<h3>').text('谜题信息'),
			$('<div class="ui four tiny statistics">').css({ marginTop: '-.5em', marginBottom: '-.5em' }).append(
				this.#addStatisticElement('id', '谜题编号'),
				this.#addStatisticElement('startDate', '开始日期'),
				this.#addStatisticElement('endDate', '结束日期')
			),
			$('<div class="ui four tiny statistics">').append(
				this.#addStatisticElement('thenCount', '获胜人数 (当天)'),
				this.#addStatisticElement('thenAverage', '平均尝试 (当天)'),
				this.#addStatisticElement('nowCount', '获胜人数 (目前)'),
				this.#addStatisticElement('nowAverage', '平均尝试 (目前)')
			),
			...userInfo
		);
		this.#statistics.id.parent().css('min-width', '50%');
	}

	render() {
		const data = this.metadata, stat = this.#statistics, $elem = this.$element;

		stat.id.html(`${data.id} <span class="stat-today-id">/ ${data.todayId}</span>`);
		stat.startDate.text(julian2str(data.startDate));
		if (data.id === data.todayId) {
			if (data.endDate > 1e8) {
				stat.endDate
					.text('待定')
					.css('color', '#ccc')
					.attr('data-tooltip', '“待定” 表示 Chordle 目前处于休眠状态，等待下一次解决谜题以激活');
			} else {
				const _3 = Number(data.endDate === data.date), _4 = julian2str(data.endDate);
				stat.endDate
					.text(_3 ? _4 : '(' + _4 + ')')
					.css('color', _1[_3])
					.attr('data-tooltip', _2[_3]);
			}
		} else
			stat.endDate.text(julian2str(data.endDate));

		stat.thenCount.text(data.thenStat.count);
		stat.thenAverage.text(avg(data.thenStat));
		stat.nowCount.text(data.nowStat.count);
		stat.nowAverage.text(avg(data.nowStat));

		if (!data.loggedIn) return;
		stat.userCountAll.text(data.userStat.countAll);
		stat.userCount.text(data.userStat.count);
		stat.winRate.text(per(data.userStat.count, data.userStat.countAll));
		stat.userAverage.text(avg(data.userStat));

		stat.userCountAllI.text(data.userStatI.countAll);
		stat.userCountI.text(data.userStatI.count);
		stat.winRateI.text(per(data.userStatI.count, data.userStatI.countAll));
		stat.userAverageI.text(avg(data.userStatI));

		stat.currentStreak.text(data.currentStreak);
		stat.maxStreak.text(data.maxStreak);

		if (!stat.chart) {
			const
				dataDistribution = getDistributionData(data.guessDistribution),
				configDistribution = {
					data: {
						labels: dataDistribution.intervals,
						datasets: [{
							label: '次数',
							data: dataDistribution.data,
							backgroundColor: dataDistribution.bgColors,
							borderColor: dataDistribution.bdColors,
							borderWidth: .5,
							barPercentage: 1 + 4e-4 * dataDistribution.intervals.length,
							categoryPercentage: 1
						}]
					},
					options: {
						maintainAspectRatio: false,
						plugins: {
							legend: { display: false },
							tooltip: chartBasicTooltip
						},
						responsive: true,
						scales: {
							x: {
								ticks: {
									...chartBasicScale.ticks,
									autoSkip: false,
									callback: (value: number) => dataDistribution.endpoints[value]
								},
								afterBuildTicks(scale) {
									scale.ticks.push({ value: scale.ticks.length });
								},
								beforeFit(scale) {
									assert(scale._startValue === -.5);
									scale._startValue = 0;
								},
								afterFit(scale) {
									assert(scale._startValue === 0);
									scale._startValue = -.5;
									scale.options.ticks.labelOffset = -.5 * scale._length / scale._valueRange;
								}
							},
							y: chartBasicScale
						}
					},
					plugins: [
						chartBasicPlugin,
						{
							beforeRender() { $elem!.modal('refresh'); }
						}
					],
					type: 'bar'
				}

			stat.chart = new Chart(stat.distCanvas, configDistribution);
		}
	}
}

const _1 = ['fuchsia', 'red'], _2 = ['粉色仅表示本谜题至多持续到该日期', '红色表示明日将会有新的谜题出现'];

function julian2str(julian: number) { return date2datestr(new Date((julian - 2440587) * 864e5), true); }
function avg(data: timeStat) { return data.count ? (data.sum / data.count).toFixed(1).replaceAll('.0', '') : '-'; }
function per(a: number, b: number) { return b ? Math.round(a * 100 / b) + '%' : '-'; }

function getDistributionData(rawData: { [key: number]: number }) {
	const
		keys = Object.keys(rawData).map(Number).sort((x, y) => x - y),
		min = keys.length ? keys[0] - 1 : 0,
		max = keys.length ? keys.at(-1)! : 1,
		nb = frontendConfig.chordle?.numberBins;
	assert(min < max);

	let numberBins = 6;
	if (checkIntRange(nb, 6, 30)) numberBins = nb;

	const
		endpoints = sep(max - min, numberBins).map(endpoint => endpoint + min),
		intervals = [], realNumberBins = endpoints.length,
		data: number[] = new Array(realNumberBins).fill(0), bgColors = [], bdColors = [];

	for (let i = 0, j = 0; i < realNumberBins; ++i) {
		for (; j < keys.length && keys[j] <= endpoints[i]; ++j)
			data[i] += rawData[keys[j]];

		const
			hue = realNumberBins === 1 ? 120 : Math.round(120 * (realNumberBins - i - 1) / (realNumberBins - 1)),
			lightness = hue >= 60 ? 40 : Math.round(50 - hue / 6);
		bgColors.push(`hsla(${hue}, 100%, ${lightness}%, 25%)`);
		bdColors.push(`hsl(${hue}, 100%, ${lightness}%)`);
		intervals.push(i && endpoints[i - 1] + 1 < endpoints[i] ? `(${endpoints[i - 1]}, ${endpoints[i]}]` : endpoints[i].toString());
	}

	endpoints.unshift(min);
	return { endpoints, intervals, data, bgColors, bdColors };
}

function sep(max: number, numberBins: number) {
	if (max <= numberBins) return range(1, max);
	assert(numberBins > 5);

	function gen(prefix: number[], n: number, alpha: number) {
		const ret = [...prefix];
		for (; ret.length !== n; ret.push(Math.ceil(ret.at(-1)! * alpha)));
		return ret;
	}

	const ret = [1];
	let lower = 1.000001, L, R, M, T = 0;
	for (; ;) {
		assert(++T < 100);
		for (L = lower, R = max; R - L > 1e-6;) {
			M = (L + R) / 2;
			const current = gen(ret, numberBins, M), final = current.at(-1)!;
			if (final === max) return current;
			final < max ? L = M : R = M;
		}
		const cL = gen(ret, numberBins, L), cR = gen(ret, numberBins, R);
		for (let i = ret.length; i < numberBins && cL[i] === cR[i]; ++i) ret.push(cL[i]);
		assert(ret.length < numberBins && cL[ret.length] + 1 === cR[ret.length]);
		ret.push(cL[ret.length]), lower = L;
	}
}
