import renderKaTeX from '/ajax/transit/render-katex';

import ModalBase from '../modal/ModalBase.js';

export default class ChordleHelper extends ModalBase {
	constructor() {
		super('ui large modal');
	}

	firstRender() {
		this.$header!.text('帮助');
		this.$content!.html(htmlDoc);
		this.$content!.find('.ui.accordion').accordion({
			animateChildren: false,
			exclusive: false,
			onChange: () => this.$element!.modal('refresh')
		});
		renderKaTeX(this.$content![0]);
	}
}

const htmlDoc = `\
<p>欢迎来到 <span style="color: fuchsia; font-size: x-large">Chordle</span>！</p>\
<p>Chordle 是一款进阶版的音乐猜谜游戏，需要具备一定 (但不多) 的乐理知识。如果你觉得 <a href="https://medle.0-th.art/" target="_blank">Medle</a> 过于简单，那么你来对地方了。</p>\
\
\
<h3>游戏规则</h3>\
<p>猜出一段音乐的<strong>和弦进行</strong>。</p>\
<p>每个谜题会给出该曲目的 BPM (速度)、调性 (主音及调式)、拍号，同时该音乐片段的节奏型会在主界面给出。这些信息可以帮助你更好地完成和弦的猜测。</p>\
<p><strong style="color: red">游戏不限猜测次数</strong>，每次猜测需要玩家给定任意一个和弦进行。每个和弦由<em class="chord-root">根音</em>和<em class="chord-type">类型</em>两个参数确定，如，属七和弦 <span class="chord-root">G</span><span class="chord-type"><sup>7</sup></span> 的根音为 <span class="chord-root">G</span>，类型为 ∗<span class="chord-type"><sup>7</sup></span> (即属七和弦)。</p>\
<p>每次猜测后，系统会有如下反馈信息：</p>\
\
<div><div class="ui ordered list">\
<div class="item">对于每个位置上的和弦，如果它和该位置上的<span style="color: fuchsia">正确和弦</span>具有相同的<em class="chord-type">类型</em>，那么这个<em class="chord-type">类型</em>将会变成<span style="color: #3c3">绿色</span>。</div>\
<div class="item">在去除掉所有<em class="chord-type">类型</em>为<span style="color: #3c3">绿色</span>的和弦 (及其位置) 后，剩下的和弦<em class="chord-type">类型</em>将会和<span style="color: fuchsia">正确和弦</span>进行<em>无序匹配</em>，每匹配到一组相同的<em class="chord-type">类型</em>，该和弦的<em class="chord-type">类型</em>就会变成<span style="color: #fa3">橙色</span>。</div>\
<div class="item">剩下的和弦<em class="chord-type">类型</em>不变色，即默认的<span class="chord-type">紫色</span>。<br /><span style="color: gray">(换句话说，前三条规则表明，就<em class="chord-type">类型</em>而言，它的反馈信息和传统的 <a href="https://www.nytimes.com/games/wordle/index.html" target="_blank">Wordle</a> 是相同的)</span></div>\
<div class="item">除此之外，为了降低 (如猜测<em class="chord-root">根音</em>的) 难度，Chordle 还提供了其它的反馈信息，具体形式为：\
<div class="list">\
<div class="item">如果一个和弦完全正确，那么这个和弦整体会被标记为<span style="color: #3c3">绿色</span>。</div>\
<div class="item">否则，该和弦整体的颜色取决于其和<span style="color: fuchsia">正确和弦</span>的<strong>相似度</strong>。相似度由某种算法计算得到，具体体现为这两个和弦替换后不协和的程度，比如，两个和弦<strong>共同音</strong>的数量和相似度有一定关系。这个颜色采用渐变机制，从<span style="color: hsl(0, 100%, 50%)">红色</span> (相似度为 0) 一直到<span style="color: hsl(86, 100%, 40%)">黄绿色</span> (除相等外相似度最高)。</div>\
</div>\
</div>\
</div></div>\
\
\
<h3>例子</h3>\
<div class="row" style="position: static">\
<div class="chord chord-type-correct correct demo-chord">\
<span class="chord-root">C</span><span class="chord-type">m<sup>7</sup></span>\
</div>\
&ensp;表示这个小七和弦 <span class="chord-root">C</span><span class="chord-type">m<sup>7</sup></span> 在完全正确的位置上，即它是一个<span style="color: fuchsia">正确和弦</span>。\
</div>\
\
<div class="row" style="margin-top: .5rem; position: static">\
<div class="chord chord-type-correct level-8 demo-chord">\
<span class="chord-root">B</span><span class="chord-type"><sup>ø7</sup></span>\
</div>\
&ensp;表示这个半减七和弦 <span class="chord-root">B</span><span class="chord-type"><sup>ø7</sup></span> 和<span style="color: fuchsia">正确和弦</span>的相似度<strong>较高</strong>，且<span style="color: fuchsia">正确和弦</span>在该位置上也是一个<strong>半减七和弦</strong>。\
</div>\
\
<div class="row" style="margin-top: .5rem; position: static">\
<div class="chord chord-type-present level-2 demo-chord">\
<span class="chord-root">D♯</span><span class="chord-type">m</span>\
</div>\
&ensp;表示这个小三和弦 <span class="chord-root">D♯</span><span class="chord-type">m</span> 和<span style="color: fuchsia">正确和弦</span>的相似度<strong>较低</strong>，且<span style="color: fuchsia">正确和弦</span>在该位置上<strong>不是</strong>小三和弦，但是 (除所有正确位置) 存在另一个<span style="color: fuchsia">正确和弦</span>的<em class="chord-type">类型</em>也是小三和弦。\
</div>\
\
<div class="row" style="margin-top: .5rem; position: static">\
<div class="chord level-0 demo-chord">\
<span class="chord-root">G</span><span class="chord-type"><sup>sus4</sup></span>\
</div>\
&ensp;表示这个挂四和弦 <span class="chord-root">G</span><span class="chord-type"><sup>sus4</sup></span> 和<span style="color: fuchsia">正确和弦</span>相去甚远，且 (除所有正确位置外)，其它<span style="color: fuchsia">正确和弦</span>均不是挂四和弦。\
</div>\
\
<p style="margin-top: .5rem">当所有和弦都变为纯<span style="color: #3c3">绿</span> (如上面的 <span class="chord-root">C</span><span class="chord-type">m<sup>7</sup></span>) 时，你就完成了游戏！</p>\
\
\
<h3>FAQ</h3>\
<div class="ui styled fluid accordion">\
<div class="title"><i class="dropdown icon"></i>为什么和弦的<em class="chord-root">根音</em>没有单独的反馈信息？</div>\
<div class="content">\
为了降低难度，本游戏<strong>不考虑和弦的转位</strong>。进一步，如果两个和弦是<a href="https://en.wikipedia.org/wiki/Enharmonic" target="_blank">同音异名 (enharmonically equivalent)</a> 的，那么本游戏不区分这两种和弦。同音异名和弦可以分为如下几种情形：\
<div class="ui ordered list">\
<div class="item"><em class="chord-root">根音</em>的同音异名：如 <span class="chord-root">F♯</span><span class="chord-type"><sup>7</sup></span> = <span class="chord-root">G♭</span><span class="chord-type"><sup>7</sup></span> 等。</div>\
<div class="item">不同<em class="chord-root">根音</em>之间的同音异名：如 <span class="chord-root">C</span><span class="chord-type"><sup>o7</sup></span> = <span class="chord-root">E♭</span><span class="chord-type"><sup>o7</sup></span> = <span class="chord-root">F♯</span><span class="chord-type"><sup>o7</sup></span> = <span class="chord-root">A</span><span class="chord-type"><sup>o7</sup></span> 等。</div>\
<div class="item">不同和弦之间的同音异名：如 <span class="chord-root">C</span><span class="chord-type"><sup>6</sup></span> = <span class="chord-root">A</span><span class="chord-type">m<sup>7</sup></span>, <span class="chord-root">C</span><span class="chord-type"><sup>sus2</sup></span> = <span class="chord-root">G</span><span class="chord-type"><sup>sus4</sup></span> 等。</div>\
</div>\
对于同音异名的和弦，在游戏中认为是完全相同的。由于这些现象的存在，<em class="chord-root">根音</em>的反馈信息不单独给出。\
</div>\
<div class="title"><i class="dropdown icon"></i>Chordle 的更新方式是怎样的？</div>\
<div class="content">\
Chordle 的更新频率和其它 <a href="https://www.nytimes.com/games/wordle/index.html" target="_blank">Wordle</a> 及其变体略有不同，它的更新是根据谜题难度动态平衡的。具体地，一个 Chordle 谜题的持续时间可能为 1 ~ 5 天不等，但一定是整天。进一步，所有的 Chordle 更新一定发生在 <strong style="color: green">20:00 UTC</strong> = 04:00 UTC +8 (即北京时间 04:00)。一个谜题的持续时间将通过如下规则决定：<div class="ui small message">在每一个 20:00 UTC 时刻进行检查，如果此时解决该谜题的人数至少为 10 个，则这个时刻会自动更新 Chordle 谜题，否则等到下一个 20:00 UTC；如果一个谜题已经完整持续了 5 天，则对应时刻会强制更新。</div>\
</div>\
<div class="title"><i class="dropdown icon"></i>Chordle 支持的和弦有哪几种？如何输入？</div>\
<div class="content">目前 Chordle 一共支持 19 种和弦，其中三和弦 6 个，七和弦 13 个。它们的名称、显示形式和<strong>可能的</strong>输入方式如下表 (和弦的输入方式有非常非常多种，欢迎大家在底下的输入框中自行探索！)\
<div class="accordion" style="margin-top: 1rem; margin-bottom: 1rem">\
<div class="title" style="color: fuchsia"><i class="dropdown icon"></i>点击展开表格</div>\
<div class="content">\
<table class="ui unstackable center aligned striped celled table">\
<thead><tr><th>名称</th><th>显示形式</th><th>输入方式 (完整)</th><th>输入方式 (正常)</th><th>输入方式 (快捷)</th></tr></thead>\
<tbody>\
<tr><td>大三和弦</td><td><span class="chord-root">C</span><span class="chord-type chord-type-major">M</span></td><td>Cmaj</td><td>C</td><td>C</td></tr>\
<tr><td>小三和弦</td><td><span class="chord-root">C</span><span class="chord-type">m</span></td><td>Cmin</td><td>Cm</td><td>Cm</td></tr>\
<tr><td>挂四和弦</td><td><span class="chord-root">C</span><span class="chord-type"><sup>sus4</sup></span></td><td>Csus4</td><td>Csus4</td><td>Cs</td></tr>\
<tr><td>增三和弦</td><td><span class="chord-root">C</span><span class="chord-type">+</span></td><td>Caug</td><td>C+</td><td>C+</td></tr>\
<tr><td>减三和弦</td><td><span class="chord-root">C</span><span class="chord-type"><sup>o</sup></span></td><td>Cdim</td><td>Co</td><td>Co</td></tr>\
<tr><td>降五和弦</td><td><span class="chord-root">C</span><span class="chord-type"><sup>♭5</sup></span></td><td>Cflat5</td><td>C♭5</td><td>Cf</td></tr>\
<tr><td>大七和弦</td><td><span class="chord-root">C</span><span class="chord-type">M<sup>7</sup></span></td><td>Cmaj7</td><td>CM7</td><td>CM7</td></tr>\
<tr><td>属七和弦</td><td><span class="chord-root">C</span><span class="chord-type"><sup>7</sup></span></td><td>Cdom7</td><td>C7</td><td>C7</td></tr>\
<tr><td>小七和弦</td><td><span class="chord-root">C</span><span class="chord-type">m<sup>7</sup></span></td><td>Cmin7</td><td>Cm7</td><td>Cm7</td></tr>\
<tr><td>半减七和弦</td><td><span class="chord-root">C</span><span class="chord-type"><sup>ø7</sup></span></td><td>Chdim7</td><td>Cø7</td><td>Ch</td></tr>\
<tr><td>减七和弦</td><td><span class="chord-root">C</span><span class="chord-type"><sup>o7</sup></span></td><td>Cdim7</td><td>Co7</td><td>Co7</td></tr>\
<tr><td>小大七和弦</td><td><span class="chord-root">C</span><span class="chord-type">m<sup>M7</sup></span></td><td>Cminmaj7</td><td>CmM7</td><td>CmM</td></tr>\
<tr><td>属七降五和弦</td><td><span class="chord-root">C</span><span class="chord-type"><sup>7♭5</sup></span></td><td>Cdom7flat5</td><td>C7♭5</td><td>C7f</td></tr>\
<tr><td>大七降五和弦</td><td><span class="chord-root">C</span><span class="chord-type">M<sup>7♭5</sup></span></td><td>Cmaj7flat5</td><td>CM7♭5</td><td>CM7f</td></tr>\
<tr><td>减大七和弦</td><td><span class="chord-root">C</span><span class="chord-type">m<sup>M7♭5</sup></span></td><td>Cdimmaj7</td><td>CmM7♭5</td><td>CoM</td></tr>\
<tr><td>增大七和弦</td><td><span class="chord-root">C</span><span class="chord-type">+<sup>M7</sup></span></td><td>Caugmaj7</td><td>C+M7</td><td>C+M</td></tr>\
<tr><td>增七和弦</td><td><span class="chord-root">C</span><span class="chord-type">+<sup>7</sup></span></td><td>Caug7</td><td>C+7</td><td>C+7</td></tr>\
<tr><td>属七挂四和弦</td><td><span class="chord-root">C</span><span class="chord-type"><sup>7sus4</sup></span></td><td>Cdom7sus4</td><td>C7sus4</td><td>C7s</td></tr>\
<tr><td>属七挂二和弦</td><td><span class="chord-root">C</span><span class="chord-type"><sup>7sus2</sup></span></td><td>Cdom7sus2</td><td>C7sus2</td><td>C7s2</td></tr>\
</tbody>\
</table>\
</div>\
</div>\
对于<em class="chord-root">根音</em>来讲，它的输入分为三种形式：\
<div class="ui ordered list">\
<div class="item">绝对音阶：即 C, D, E, F, G, A, B。</div>\
<div class="item">相对音阶：即 1, 2, 3, 4, 5, 6, 7。</div>\
<div class="item">级数表示：即 I, II, III, IV, V, VI, VII。</div>\
</div>\
在这三种表示法中，后两种都是相对音阶，即它们对应的音<strong>取决于谜题曲目的主音</strong>。这两种表示的区别在于对小调的表示：以 <span class="chord-root">A</span> <span class="tonality-scale minor">小调</span>为例，相对音阶 1 ~ 7 仍然表示 <span class="chord-root">C</span> ~ <span class="chord-root">B</span>，而级数 I ~ VII 表示 <span class="chord-root">A</span> ~ <span class="chord-root">G</span>。\
</div>\
<div class="title"><i class="dropdown icon"></i>我可以玩之前的谜题吗？如果可以，该如何进入？</div>\
<div class="content">\
<div style="display: none"></div>你可以玩之前的谜题。具体地，有两种进入的方式：\
<div class="ui ordered list">\
<div class="item">若你知道你想玩的谜题编号，设其为 \\(x\\)，则访问链接 ${location.origin}/chordle?id=\\(x\\) 即可进入，记得将 \\(x\\) 替换为对应的编号。</div>\
<div class="item">在<a href="/chordle/list">这里</a>有迄今为止所有谜题的列表，可以查看自己的解答情况并选择进入。</div>\
</div>\
无论采用哪种方式，要注意的一点是，玩之前的谜题的统计方式和玩当天谜题是不一样的，如，它<strong>不影响</strong>任何<strong>连胜</strong>相关的数据 (即无法计入连胜)，在统计胜率和游戏次数时也会单独统计。\
</div>\
</div>\
`;
