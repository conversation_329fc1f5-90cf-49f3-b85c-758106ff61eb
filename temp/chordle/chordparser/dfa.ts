import type { ChordType } from '../../models/chordle';

interface DFASchema {
	k: [ChordType, number];
	t: (number | undefined)[];
}

interface DFANode {
	k: [ChordType, number];
	t: (DFANode | undefined)[];
}

const
	chtype = [
		'#+⌗♯', '-‐‑‒–—―−', '2', '4', '5', '6', '7', 'Aa',
		'BFbf♭', 'Dd', 'Gg', 'HhØøΦφ∅⌀', 'Ii', 'Jj', 'M',
		'm', 'Nn', 'Oo°', 'Ss', 'Uu', 'Δ∆△', '♮'
	],
	ch2type: Map<string, number> = new Map(),
	// eslint-disable-next-line no-sparse-arrays
	dfaSchema: DFASchema[] = [{k:['major',0],t:[1,2,,,,3,4,1,5,6,,7,,,8,9,,10,11,,12]},{k:['aug',0],t:[,,,,,,13,,,,,,,,14,14,,,,,14]},{k:['minor',0],t:[15,16,,,,17,18,,16,,,,,,15,15,,,,,15,15]},{k:['min7',9],t:[,19,,,,,,,19]},{k:['dom7',0],t:[13,20,,,,,,,20,,,,,,,,,,21]},{k:['flat5',0],t:[]},{k:['dim',0],t:[,,,,,,,,,,,,22,,,,,23]},{k:['hdim7',0],t:[]},{k:['major',0],t:[24,5,,,,3,25,26,5,,,,2,12,,,2]},{k:['minor',0],t:[15,16,,,,17,18,12,16,,,,2,12,15,15,2,,,,15,15]},{k:['dim',0],t:[,,,,,,27,,,,,,,,28,28]},{k:['sus4',0],t:[,,29,30]},{k:['major',0],t:[24,5,,,,3,25,24,5]},{k:['aug7',0],t:[]},{k:['augmaj7',0],t:[]},{k:['minmaj7',0],t:[,28,,,,17,,,28]},{k:['dim',0],t:[,,,,31,,27]},{k:['hdim7',9],t:[,27,,,,,,,27]},{k:['min7',0],t:[,7,,,,,,,7,,,7,,,,,,7]},{k:['hdim7',6],t:[]},{k:['dom7flat5',0],t:[]},{k:['dom7sus4',0],t:[,,32,33]},{k:['dim',0],t:[,,,,,,27,,,,,,,,10,10]},{k:['dom7',0],t:[,20,,,,,,,20,,,,,,,,,,21]},{k:['aug',0],t:[,,,,,,14]},{k:['maj7',0],t:[14,34,,,,,,,34]},{k:['major',0],t:[24,,,,,,25,24,,,24,,,12,,,,,,24]},{k:['dim7',0],t:[]},{k:['dimmaj7',0],t:[]},{k:['sus4',7],t:[]},{k:['sus4',0],t:[]},{k:['dim',0],t:[]},{k:['dom7sus2',0],t:[]},{k:['dom7sus4',0],t:[]},{k:['maj7flat5',0],t:[]}],
	dfa: DFANode[] = dfaSchema.map(({k}) => ({k, t: []}));

chtype.forEach((cat, idx) => [...cat].forEach(ch => ch2type.set(ch, idx)));
dfa.forEach((node, idx) => node.t = dfaSchema[idx].t.map(ref => dfa[ref!]));

export function parse(text: string): [ChordType, number] {
	let node = dfa[0];
	for (const char of text) {
		const w: number = ch2type.get(char)!;
		if (Object.hasOwn(node.t, w)) node = node.t[w]!;
	}
	return node.k;
}
