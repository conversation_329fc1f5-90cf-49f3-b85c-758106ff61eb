import type Chord from './chord.js';
import ChordTypes from './chordtypes.js';

const weightTable = {
	// triads
	major: [.666667, .471405, .57735],
	minor: [.69282, .469042, .547723],
	sus4: [.655744, .52915, .538516],
	aug: [.57735, .57735, .57735],
	dim: [.640312, .447214, .6243],
	flat5: [.6243, .5, .6],
	// four notes (seventh)
	maj7: [.591608, .447214, .5, .447214],
	dom7: [.5, .565685, .360555, .547723],
	min7: [.519615, .519615, .458258, .5],
	hdim7: [.565685, .360555, .547723, .5],
	dim7: [.5, .5, .5, .5],
	minmaj7: [.565685, .458258, .519615, .447214],
	dom7flat5: [.5, .5, .5, .5],
	maj7flat5: [.538516, .374166, .519615, .547723],
	dimmaj7: [.519615, .374166, .538516, .547723],
	augmaj7: [.519615, .447214, .5, .52915],
	aug7: [.469042, .52915, .469042, .52915],
	dom7sus4: [.509902, .52915, .458258, .5],
	dom7sus2: [.469042, .5, .52915, .5]
}

const mem = new ArrayBuffer(192), a = new Float64Array(mem, 0, 12), b = new Float64Array(mem, 96, 12);

export function chordSimilarity(chord1: Chord, chord2: Chord) {
	let S: number, p: number, res = 0; a.fill(0), b.fill(0);

	S = ChordTypes[chord1.type] << chord1.root;
	for (const w of weightTable[chord1.type]) p = 31 - Math.clz32(S & -S), a[p % 12] += w, S &= S - 1;

	S = ChordTypes[chord2.type] << chord2.root;
	for (const w of weightTable[chord2.type]) p = 31 - Math.clz32(S & -S), b[p % 12] += w, S &= S - 1;

	for (p = 0; p < 12; ++p) res += a[p] * b[p];

	res = res < .25 ? 0 : Math.tan(2.847404 * res - 1.898269) * .351197 + .666667;
	return Math.min(Math.max(res, 0), 1);
}
