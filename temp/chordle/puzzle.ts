import type { Player } from 'tone';

import type { ChordType, Tonality } from '../models/chordle';
import { assert } from '../util/type.js';
import Chord from './chord.js';

const synthConfig = <const>{
	harmonicity: 8,
	modulationIndex: 2,
	oscillator: {
		type: 'sine'
	},
	envelope: {
		attack: .001,
		decay: 2,
		sustain: .1,
		release: 2
	},
	modulation: {
		type: 'square'
	},
	modulationEnvelope: {
		attack: .002,
		decay: .2,
		sustain: 0,
		release: .2
	}
}, vibratoConfig = <const>{
	frequency: 5,
	depth: .1,
	type: 'sine'
}, LRU_LIMIT = 64;

export function getSynth() {
	const synth = new Tone.PolySynth(Tone.FMSynth, synthConfig).toDestination();
	synth.chain(
		new Tone.Vibrato(vibratoConfig),
		new Tone.Reverb(6),
		Tone.Destination
	).maxPolyphony = 64;
	return synth;
}

interface RawPuzzle extends Omit<Puzzle, 'beatOffset' | 'beats' | 'chords' | 'duration' | 'id'> {
	chords: [ChordType | 'rest', number, number][];
	beatOffset?: number;
}

export default class Puzzle {
	id: number;
	bpm: number;
	title: string;
	author: string;
	description: string;
	tonality: Tonality;
	timeSignature: [number, number];
	url: string;
	volume: number;
	offset: number;
	chords: { chord: Chord | null, duration: number }[];
	duration: number;
	beats: number;
	beatOffset: number;

	constructor(id: number, config: RawPuzzle) {
		this.id = id;
		this.bpm = config.bpm;
		this.title = config.title;
		this.author = config.author;
		this.description = config.description;
		this.tonality = config.tonality;
		this.timeSignature = config.timeSignature;
		this.url = config.url;
		this.volume = config.volume;
		this.offset = config.offset; // in seconds
		this.chords = config.chords.map(([type, root, duration]) => (
			{ chord: type === 'rest' ? null : Chord.fromCache(type, root), duration }
		));
		this.duration = this.chords.map(segment => segment.duration).reduce((x, y) => x + y); // in beats
		this.beats = Math.ceil(this.duration * this.timeSignature[1] / 4);
		this.beatOffset = config.beatOffset ?? 0;
	}

	async #fillBuffer(player: Player) {
		const
			url = `/attachment/chordle/${this.url}`,
			cache = await caches.open('chordle'),
			set1 = new Set((await cache.keys()).map(key => key.url.substring(key.url.lastIndexOf('/') + 1))),
			lru = (JSON.parse(localStorage.getItem('chordle-lru')!) ?? []).filter((entry: string) => set1.has(entry)),
			index = lru.indexOf(this.url);
		if (~index) {
			lru.splice(index, 1);
			lru.push(this.url);
		} else {
			lru.push(this.url);
			if (lru.length > LRU_LIMIT) lru.shift();
			await cache.add(url);
		}
		localStorage.setItem('chordle-lru', JSON.stringify(lru));

		const response = await cache.match(url), set2 = new Set(lru);
		assert(response);

		set1.forEach(key => set2.has(key) || cache.delete(`/attachment/chordle/${key}`));
		player.buffer = await Tone.getContext().decodeAudioData(await response.arrayBuffer());
	}

	async getPlayer() {
		const player = new Tone.Player({ volume: this.volume });
		await this.#fillBuffer(player);
		return player.toDestination();
	}
}
