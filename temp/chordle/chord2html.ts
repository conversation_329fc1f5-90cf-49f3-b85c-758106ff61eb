import type { ChordType, Tonality } from '../models/chordle';
import { frontendConfig, htmlSanitize } from '../util/web.js';
import type Chord from './chord.js';

type NoteConverter = (note: number, tonality: Tonality) => string;

const
	et12 = [['C'], ['C♯', 'D♭'], ['D'], ['D♯', 'E♭'], ['E'], ['F'], ['F♯', 'G♭'], ['G'], ['G♯', 'A♭'], ['A'], ['A♯', 'B♭'], ['B']],
	nm12 = {
		major: ['1', '♯1', '2', '♭3', '3', '4', '♯4', '5', '♭6', '6', '♭7', '7'],
		minor: ['1', '♯1', '2', '♯2', '3', '4', '♯4', '5', '♯5', '6', '♭7', '7']
	},
	sr12 = {
		major: ['I', '♯I', 'II', '♭III', 'III', 'IV', '♯IV', 'V', '♭VI', 'VI', '♭VII', 'VII'],
		minor: ['I', '♭II', 'II', 'III', '♯III', 'IV', '♯IV', 'V', 'VI', '♯VI', 'VII', '♯VII']
	},
	ac12 = [0x508, 0x54a, 0x400, 0x54a, 0, 0x50a, 0x54a, 0x408, 0x54a, 0, 0x54a, 0],
	noteConverters: { [key: string]: NoteConverter } = {
		absolute(note: number, tonality: Tonality) {
			const tonic = (tonality.tonic + (tonality.scale === 'minor' ? 3 : 0)) % 12;
			return et12[note][ac12[tonic] >> note & 1];
		},
		numbered(note: number, tonality: Tonality) {
			const tonic = (tonality.tonic + (tonality.scale === 'minor' ? 3 : 0)) % 12;
			return nm12[tonality.scale][(note - tonic + 12) % 12];
		},
		series(note: number, tonality: Tonality) {
			return sr12[tonality.scale][(note - tonality.tonic + 12) % 12];
		}
	}

// this is modifiable in '/preference' !
const chordHtmlDefault = {
	// triads
	major: '<span class="chord-root">@</span><span class="chord-type chord-type-major">M</span>',
	minor: '<span class="chord-root">@</span><span class="chord-type">m</span>',
	sus4: '<span class="chord-root">@</span><span class="chord-type"><sup>sus4</sup></span>',
	aug: '<span class="chord-root">@</span><span class="chord-type">+</span>',
	dim: '<span class="chord-root">@</span><span class="chord-type"><sup>o</sup></span>',
	flat5: '<span class="chord-root">@</span><span class="chord-type"><sup>♭5</sup></span>',
	// four notes (seventh)
	maj7: '<span class="chord-root">@</span><span class="chord-type">M<sup>7</sup></span>',
	dom7: '<span class="chord-root">@</span><span class="chord-type"><sup>7</sup></span>',
	min7: '<span class="chord-root">@</span><span class="chord-type">m<sup>7</sup></span>',
	hdim7: '<span class="chord-root">@</span><span class="chord-type"><sup>ø7</sup></span>',
	dim7: '<span class="chord-root">@</span><span class="chord-type"><sup>o7</sup></span>',
	minmaj7: '<span class="chord-root">@</span><span class="chord-type">m<sup>M7</sup></span>',
	dom7flat5: '<span class="chord-root">@</span><span class="chord-type"><sup>7♭5</sup></span>',
	maj7flat5: '<span class="chord-root">@</span><span class="chord-type">M<sup>7♭5</sup></span>',
	dimmaj7: '<span class="chord-root">@</span><span class="chord-type">m<sup>M7♭5</sup></span>',
	augmaj7: '<span class="chord-root">@</span><span class="chord-type">+<sup>M7</sup></span>',
	aug7: '<span class="chord-root">@</span><span class="chord-type">+<sup>7</sup></span>',
	dom7sus4: '<span class="chord-root">@</span><span class="chord-type"><sup>7sus4</sup></span>',
	dom7sus2: '<span class="chord-root">@</span><span class="chord-type"><sup>7sus2</sup></span>'
}, chordHtml: { [key in ChordType]?: string } = {},
	wl = [
		'b', 'code', 'del', 'dfn', 'em', 'i', 'ins', 'kbd', 's', 'samp', 'small', 'span', 'strong', 'sub', 'sup', 'tt', 'var', 'u'
	];

let noteConverter: NoteConverter;

function getTemplate(type: ChordType) {
	try {
		const template = frontendConfig.chordle?.chordHtml?.[type];
		if (typeof template !== 'string') throw new TypeError;
		return htmlSanitize(template, wl);
	} catch {
		return chordHtmlDefault[type];
	}
}

function getNoteConverter() {
	const type: string = frontendConfig.chordle?.noteConvert;
	return noteConverters[Object.hasOwn(noteConverters, type) ? type : 'absolute'];
}

export function updateNoteConverter() {
	return noteConverter = getNoteConverter();
}

export function chord2html(this: Chord, tonality: Tonality) {
	return (chordHtml[this.type] ??= getTemplate(this.type)).replace('@', noteConverter(this.root, tonality));
}
