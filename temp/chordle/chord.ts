import type { FMSynth, PolySynth, MidiClass } from 'tone';

import type { ChordType, Tonality } from '../models/chordle';
import { range } from '../util/nt.js';
import { updateNoteConverter, chord2html } from './chord2html.js';
import { parseChord } from './chordparser.js';
import { chordSimilarity } from './chordsimilarity.js';
import ChordTypes from './chordtypes.js';

type PitchWithVelocity = [MidiClass, number];

export default class Chord {
	type: ChordType;
	root: number;
	#notes?: PitchWithVelocity[];

	constructor(type: ChordType, root: number) {
		this.type = type;
		this.root = root;
	}

	notesSet() {
		const set = ChordTypes[this.type] << this.root;
		return (set ^ set >> 12) & 0xfff;
	}

	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	toHTML(tonality: Tonality): string { return ''; }

	toJSON() { return [this.type, this.root]; }

	play(synth: PolySynth<FMSynth>) {
		synth.releaseAll();
		for (const [pitch, vel] of this.notes) synth.triggerAttackRelease(pitch, .1, undefined, vel);
	}

	get notes() { return this.#notes ??= generateNotes(this); }

	static fromNotesSet(set: number) { return chords[set]; }

	static fromCache(type: ChordType, root: number) {
		const ret = cache[type]?.[root];
		if (!ret) throw new ReferenceError(`chord ${root} ${type} not found`);
		return ret;
	}

	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	static parse(string: string, tonality: Tonality): Chord | null { return null; }

	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	static similarity(chord1: Chord, chord2: Chord): number { return 0; }

	static updateNoteConverter() {
		// will be overwritten
	}
}

Chord.prototype.toHTML = chord2html;
Chord.parse = parseChord;
Chord.similarity = chordSimilarity;
Chord.updateNoteConverter = updateNoteConverter;

const
	cache: { [type in ChordType]?: Chord[] } = {},
	chords: Chord[] = [],
	midi = range(0, 127).map(value => Tone.Midi(value));

for (const [type, root] of <[ChordType, number][]>Object.entries(ChordTypes)) {
	const cur: Chord[] = cache[type] = [];
	for (let set = root, i = 0; i < 12; ++i) {
		const chord = new Chord(type, i);
		cur.push(chord);
		chords[set] ??= chord;
		if ((set <<= 1) > 0x1000) set ^= 0x1001;
	}
}

function getVelocity(value: number) { return Math.exp(-((value - 66) ** 2) / 80); }

function generateNotes(chord: Chord) {
	const set: number[] = [], setMask = chord.notesSet(), ret: PitchWithVelocity[] = [];
	for (let note = 0; note < 12; ++note)
		if (setMask >> note & 1) set.push(note);
	for (const octave of <const>[48, 60, 72])
		for (const note of set) {
			const value = octave + note, velocity = getVelocity(value);
			if (velocity > .05) ret.push([midi[value], velocity]);
		}
	return ret;
}
