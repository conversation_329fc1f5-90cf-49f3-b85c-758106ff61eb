import ChordTypes from '../chordle/chordtypes';

export type ChordType = keyof typeof ChordTypes;

export interface Tonality {
	scale: 'major' | 'minor';
	tonic: number;
}

export interface timeStat {
	count: number;
	sum: number;
}

export interface timeStatWithAll extends timeStat {
	countAll: number
}

export interface ChordleMetadata {
	loggedIn: boolean;
	id: number;
	todayId: number;
	startDate: number;
	endDate: number;
	date: number;
	thenStat: timeStat;
	nowStat: timeStat;
	userStat: timeStatWithAll;
	userStatI: timeStatWithAll;
	currentStreak: number;
	maxStreak: number;
}
