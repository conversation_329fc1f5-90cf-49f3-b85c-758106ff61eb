import ChordleHelper from '../chordle/ChordleHelper.js';
import ChordleSettings from '../chordle/ChordleSettings.js';
import ChordleStatistics from '../chordle/ChordleStatistics.js';
import Chord from '../chordle/chord.js';
import Puzzle, { getSynth } from '../chordle/puzzle.js';
import Counter from '../counter.js';
import Modals from '../modal/Modals.js';
import User from '../user.js';
import { deAES } from '../util/crypto.js';
import { assert, checkIntRange, err2lines } from '../util/type.js';
import { frontendConfig, post, authPost, setAndSaveConfig, bootstrap } from '../util/web.js';

// globals
const params = new URLSearchParams(location.search);
let puzzle, metadata = {}, duration = null;
let guesses = [], win = false, schid = -1, schme = -1;
let col; // number of columns
let focused = -1; // current column
let playState = -1; // current row
let interfaces, widthCache;
const widthCaches = { absolute: new WeakMap(), numbered: new WeakMap(), series: new WeakMap() };
// $elements
let $musicLoading, $dashboard, $stage, $bars, $guesses = [];
// functions
let resizeChord, resizeChords;
// Tone.js
let context, transport, synth, syncedSynth, player, metronomes;

function beatPattern(n, q) { return q % n ? (n % 3 ? (n | q) & 1 : q % 3) ? 'weak' : 'medium' : 'strong'; }

async function initPuzzle() {
	let id = params.get('id'), response;
	if (!checkIntRange(id = Number(id), 1, Infinity)) id = null;

	if (metadata.loggedIn = Boolean(User.current)) {
		const
			raw = await post('/chordle/data', {
				uid: User.current.uid,
				...id && { id },
				userInfo: true,
				userGuess: true
			}),
			json = (await deAES(raw.result, User.current)).asUtf8();
		response = JSON.parse(json);
	} else
		response = (await post('/chordle/data', { ...id && { id } })).result;

	metadata.id = response.id;
	metadata.todayId = response.todayId;
	Object.assign(metadata, response.metadata);

	puzzle = await (await fetch(`/chordle/data/${metadata.id}`)).json();
	if (puzzle.error) {
		const { error } = puzzle, index = error.indexOf(': ');
		throw new globalThis[error.substring(0, index)](error.substring(index + 2));
	}
	puzzle = new Puzzle(metadata.id, puzzle);

	guesses = (response.guesses ?? []).map(row => row.map(args => Chord.fromCache(...args)));
	widthCache = widthCaches[Chord.updateNoteConverter().name];
}

function initSynth() { // init Tone.js
	context = Tone.getContext();
	transport = Tone.getTransport();
	assert(context.transport === transport && transport.context === context && Tone.Transport === transport);

	transport.bpm.value = puzzle.bpm;
	transport.timeSignature = puzzle.timeSignature;
	puzzle.getPlayer().then(result => {
		player = result.sync();
		duration = player.buffer.duration;
		assert(duration > puzzle.offset + 60 / puzzle.bpm * puzzle.duration);
		$musicLoading?.hide();
	});
	metronomes = new Tone.Players({
		urls: {
			'medium': 'medium.wav',
			'strong': 'strong.wav',
			'weak': 'weak.wav'
		},
		baseUrl: '/attachment/chordle/metronome/'
	}).toDestination();

	synth = getSynth();
	syncedSynth = getSynth().sync();
}

function renderMetadata() {
	$('head').append('<link rel="icon" type="images/png" href="/attachment/chordle/icon.png">');
	$('.chordle-id').text(puzzle.id);
	if (!metadata.loggedIn) $('.ui.visitor.message').show();
	if (puzzle.id !== metadata.todayId && !params.has('force')) {
		const another = new URLSearchParams(params), $message = $('.ui.goto-today.message');
		another.set('force', true);
		$message.children('a:last-child').attr('href', '/chordle?' + another);
		$message.children('span').text(metadata.todayId);
		$message.show();
	}

	const
		_1 = {
			major: ['C', 'D♭', 'D', 'E♭', 'E', 'F', 'G♭', 'G', 'A♭', 'A', 'B♭', 'B'],
			minor: ['C', 'C♯', 'D', 'E♭', 'E', 'F', 'F♯', 'G', 'G♯', 'A', 'B♭', 'B']
		},
		_2 = { major: '大调', minor: '小调' },
		buttons = {};

	$('.bpm').text(puzzle.bpm);
	$('.chord-root').text(_1[puzzle.tonality.scale][puzzle.tonality.tonic]);
	$('.tonality-scale').text(_2[puzzle.tonality.scale]).addClass([puzzle.tonality.scale]);
	$('.time-signature').text(puzzle.timeSignature.join('/'));
	$('.chordle-header>button').each(function () { buttons[this.firstChild.classList[0]] = this; });
	$('.answer-area .title').text(puzzle.title);
	$('.answer-area .author').text(puzzle.author);
	$('.answer-area .description').html(puzzle.description);

	interfaces = {
		helper: new ChordleHelper(),
		statistics: new ChordleStatistics(metadata),
		settings: new ChordleSettings({
			onChange(noteConvert) {
				if (frontendConfig.chordle?.noteConvert === noteConvert) return;
				setAndSaveConfig(['chordle', 'noteConvert'], noteConvert);
				widthCache = widthCaches[Chord.updateNoteConverter().name];
				for (let i = 0; i < guesses.length; ++i)
					for (let j = 0; j < col; ++j)
						if (guesses[i][j] instanceof Chord)
							$guesses[i][j].firstChild.innerHTML = guesses[i][j].toHTML(puzzle.tonality);
				resizeChords();
			},
			latencyTest,
			latencyTestEnd,
			transport
		})
	};

	$(buttons.question).on('click', () => interfaces.helper.show());
	$(buttons.chart).on('click', () => interfaces.statistics.show());
	$(buttons.setting).on('click', () => interfaces.settings.show());
}

function renderDashboard() {
	function renderStageAndBeats() {
		const n = puzzle.timeSignature[0], _1 = { medium: 1.25, strong: 1.5, weak: 1 }, $s = [], $b = [];
		let b = 0, q = 0, count = 1;
		for (let i = puzzle.beatOffset; i <= puzzle.beatOffset + puzzle.beats; ++i) {
			b = Math.floor(i / n), q = i % n;
			if (q < 0) q += n;
			if (i === puzzle.beatOffset + puzzle.beats) {
				$s.push($(`<div class="${beatPattern(n, q)}">`));
				if (q) {
					$b.push($(`<div class="brief beat">`).text(b + 1).css('flex-grow', q / n));
				}
			} else {
				$s.push($(`<div class="${beatPattern(n, q)} beat">`));
				$b.push($(`<div class="detailed beat">`).text(`${b + 1}:${q + 1}`));
				if (q === n - 1) {
					$b.push($(`<div class="brief beat">`).text(b + 1).css('flex-grow', count === n ? null : count / n));
					count = 1;
				} else {
					++count;
				}
			}
		}
		$stage.append($s), $bars.append($b);
		$dashboard.css('--stage-width', `calc(100% - ${50 + _1[beatPattern(n, q)]}px)`);
	}

	function getWidth(chord) {
		if (!(chord instanceof Chord)) return 0;
		if (!widthCache.has(chord))
			widthCache.set(chord, $jkw.html(chord.toHTML(puzzle.tonality)).outerWidth());
		return widthCache.get(chord);
	}

	resizeChord = function (row, col) {
		const chord = guesses[row][col], elem = $guesses[row][col];
		elem.clientWidth < getWidth(chord) && elem.textContent[0] !== '\ud83d' ? elem.classList.add('tight') : elem.classList.remove('tight');
	}

	resizeChords = function () {
		for (let i = 0; i < guesses.length; ++i)
			for (let j = 0; j < col; ++j)
				resizeChord(i, j);
	}

	function resizeAdjust() {
		resizeChords();
		$bars.toggleClass(['brief'], $bars.outerWidth() < 27.890625 * puzzle.beats + 50);
		$inputArea.outerWidth($dashboard.outerWidth());
	}

	const $inputArea = $('.input-area'), $jkw = $('.jkw');

	$dashboard = $('.dashboard');
	$stage = $dashboard.children('.stage');
	$bars = $dashboard.children('.bars');
	$musicLoading = $('.ui.music-loading.message');
	if (duration == null) $musicLoading.show();

	renderStageAndBeats();

	resizeAdjust(), $(window).on('RAFresize', resizeAdjust);
}

function gameHandler() {
	function generatePuzzlePattern() {
		const [n, d] = puzzle.timeSignature, _1 = { medium: 1.25, strong: 1.5, weak: 1 }, ret = [];
		let _2 = 0, _3;
		for (const segment of puzzle.chords) {
			_3 = _2 + segment.duration;
			if (segment.chord instanceof Chord) {
				const L = Math.round(_2 * d * 4) / 16, R = Math.round(_3 * d * 4) / 16;
				let lo = .5, ro = .5;
				if (Number.isSafeInteger(L)) lo = 3 + _1[beatPattern(n, L + puzzle.beatOffset)];
				if (Number.isSafeInteger(R)) ro = 3;
				ret.push({
					left: L ? `calc(var(--stage-width) * ${L / puzzle.beats} + ${lo + 50}px)` : lo + 50,
					width: lo + ro ? `calc(var(--stage-width) * ${(R - L) / puzzle.beats} - ${lo + ro}px)` : `calc(var(--stage-width) * ${(R - L) / puzzle.beats})`
				});
			}
			_2 = _3;
		}
		return ret;
	}

	function createRow(index) {
		return $('<div class="row">').css('top', 44 + 50 * index).append($play.clone(), ...puzzlePattern.map(css => $('<div class="chord">').css(css).append($('<div>').html('&nbsp;'))));
	}

	function isFull(guess) { return guess.every(chord => chord instanceof Chord); }

	function parse(string, col) {
		return string.trim() === '-' && guesses.length > 1 ? guesses.at(-2)[col] : Chord.parse(string, puzzle.tonality);
	}

	function judge(guess, $guess) {
		const typeCounter = new Counter();
		let i, accepted = true;
		assert(guess.length === col && isFull(guess));
		for (i = 0; i < col; ++i)
			if (guess[i].type === std[i].type) $guess[i].classList.add('chord-type-correct');
			else typeCounter.update(std[i].type);
		for (i = 0; i < col; ++i)
			if (guess[i].type !== std[i].type && typeCounter.get(guess[i].type))
				$guess[i].classList.add('chord-type-present'), typeCounter.update(guess[i].type, -1);
		for (i = 0; i < col; ++i) {
			// we should use notesSet to regard C+ (Caug) as same as E+ (Eaug), etc.
			if (guess[i].notesSet() === std[i].notesSet())
				$guess[i].classList.add('correct');
			else {
				accepted = false;
				$guess[i].classList.add(`level-${Math.floor(Chord.similarity(guess[i], std[i]) * 10)}`);
			}
		}
		return accepted;
	}

	function set(column, chord) {
		if ((guesses.at(-1)[column] = chord) instanceof Chord) {
			$guesses.at(-1)[column].firstChild.innerHTML = chord.toHTML(puzzle.tonality);
			resizeChord(guesses.length - 1, column);
			if (isFull(guesses.at(-1))) $okButton.removeClass(['disabled']);
		} else {
			$guesses.at(-1)[column].firstChild.innerHTML = '&nbsp;';
			if (!isFull(guesses.at(-1))) $okButton.addClass(['disabled']);
		}
	}

	function focus(newFocused) {
		if (focused !== newFocused) {
			const $guess = $guesses.at(-1);
			$guess[focused]?.classList.remove('focused');
			$guess[focused = newFocused]?.classList.add('focused');
			~focused ? $input.removeClass(['disabled']) : $input.addClass(['disabled']);
			~focused ? $inputE.val('').focus() : $inputE.val('').blur();
		} else if (~focused) $inputE.focus();
	}

	function focusPrev() { focus((focused <= 0 ? col : focused) - 1); }
	function focusNext() { focus(focused === col - 1 ? 0 : focused + 1); }

	function winCallback() {
		$containsSong[0].checked = true;
		$containsSong.parent().addClass(['checkbox']);
		$inputArea.hide(), $answerArea.show();
	}

	function attempt() {
		if (!metadata.loggedIn) return Modals.chordleLogin().show();

		focus(-1);
		$okButton.addClass(['disabled']);
		$copyButton.removeClass(['disabled']);

		if (judge(guesses.at(-1), $guesses.at(-1))) win = true;

		authPost('/chordle/submit', JSON.parse(JSON.stringify({ id: puzzle.id, win, guesses })));

		if (win) {
			guesses[guesses.length - 1] = [...std];
			for (let c = 0; c < col; ++c)
				$guesses.at(-1)[c].firstChild.innerHTML = guesses.at(-1)[c].toHTML(puzzle.tonality);
			return winCallback();
		}

		const _1 = createRow(guesses.length);
		$guesses.push(_1.find('.chord'));
		guesses.push(new Array(col).fill(null));

		$dashboard.append(_1);
		$stage.outerHeight(34 + 50 * guesses.length);
		focus(0);
		_1[0].scrollIntoView({ behavior: 'smooth', block: 'start' });
	}

	const
		$inputArea = $('.input-area'),
		$input = $inputArea.find('.chord.input'),
		$inputE = $input.children(),
		$okButton = $inputArea.find('.ok.button'),
		$copyButton = $inputArea.find('.copy.button'),
		$answerArea = $('.answer-area'),
		$containsSong = $answerArea.find('.contains-song>input'),
		$enableMask = $answerArea.find('.enable-mask>input'),
		$play = $('<div class="header"><button class="ui icon circular primary button"><i class="play icon"></i></button></div>'),
		puzzlePattern = generatePuzzlePattern(),
		std = puzzle.chords.map(segment => segment.chord).filter(chord => chord instanceof Chord);

	assert((col = puzzlePattern.length) && col === std.length);

	{
		const _1 = guesses.map((_, i) => createRow(i));
		$guesses = _1.map($row => $row.find('.chord'));

		for (let i = 0; i < guesses.length; ++i) {
			if (judge(guesses[i], $guesses[i])) win = true, guesses[i] = [...std];
			for (let j = 0; j < col; ++j)
				$guesses[i][j].firstChild.innerHTML = guesses[i][j].toHTML(puzzle.tonality);
		}

		if (guesses.length) $copyButton.removeClass(['disabled']);

		if (win) winCallback();
		else {
			const _2 = createRow(guesses.length);
			$guesses.push(_2.find('.chord'));
			guesses.push(new Array(col).fill(null));
			_1.push(_2);
		}

		$dashboard.append(_1);
		$stage.outerHeight(34 + 50 * guesses.length);
		if (!win) focus(0);
		_1.at(-1)[0].scrollIntoView({ behavior: 'smooth', block: 'start' });
	}

	resizeChords();

	$dashboard
		.on('click', '.chord', function () {
			let i, j;
			for (i = 0; i < $guesses.length && !~(j = $guesses[i].index(this)); ++i);
			if (i === $guesses.length) return;
			if (i === $guesses.length - 1 && !win) focus(j);
			if (guesses[i][j] instanceof Chord) guesses[i][j].play(synth);
		})
		.on('click', 'button', function () {
			let i; const next = this.parentNode.nextSibling;
			for (i = 0; i < $guesses.length && $guesses[i][0] !== next; ++i);
			if (i === playState) {
				stop();
				playState = -1;
			} else if (i !== $guesses.length) {
				if (~playState) stop();
				playState = i;
				this.parentNode.parentNode.classList.add('active');
				assert(this.firstChild.classList.replace('play', 'stop'));
				play(guesses[i], $containsSong[0].checked);
			}
		});

	$(document)
		.on('keydown', e => {
			if (win) return;
			const inModal = e.target.closest('.modal'), inText = e.target === $inputE[0] ? $inputE.val().length : inModal;
			switch (e.code || e.key) {
				case 'Escape': return focus(-1);
				case 'Backspace': return inText || (set(focused, null), focusPrev(), e.preventDefault());
				case 'Delete': return inText || (set(focused, null), focusNext(), e.preventDefault());
				case 'ArrowLeft': return inText || (focusPrev(), e.preventDefault());
				case 'ArrowRight': return inText || (focusNext(), e.preventDefault());
				case 'Tab': return inModal || (e.shiftKey ? focusPrev() : focusNext(), e.preventDefault());
			}
		})
		.on('keypress', e => {
			if (win) return;
			switch (e.code || e.key) {
				case 'Enter': {
					if (e.shiftKey) {
						if (isFull(guesses.at(-1))) attempt();
						else { let i = focused + 1; for (; i === col && (i = 0), guesses.at(-1)[i] instanceof Chord; ++i); focus(i); }
					} else focusNext();
					e.preventDefault();
					break;
				}
			}
		});

	$inputArea.find('.prev.button').click(focusPrev);
	$inputArea.find('.next.button').click(focusNext);
	$okButton.click(attempt);
	$copyButton.click(e => {
		if (guesses.length < 2) return;
		if (e.shiftKey) guesses.at(-2).forEach((chord, column) => set(column, chord));
		else if (~focused) set(focused, guesses.at(-2)[focused]), focusNext();
	});
	$inputArea.find('.clear.button').click(e => {
		if (e.shiftKey) for (let i = 0; i < col; ++i) set(i, null);
		else if (~focused) set(focused, null), focusNext();
	});
	$inputE
		.onInput(() => ~focused && set(focused, parse($inputE.val(), focused)))
		.on('paste', e => {
			if (!~focused || $inputE.val().length) return;

			const data = e.originalEvent.clipboardData.getData('text/plain');
			if (typeof data !== 'string') return;

			const list = data.split(/[\t\n]/g);
			if (list.length < 2) return;

			let i = focused, j;
			for (const term of list) set(i, parse(term, i)), j = i, ++i === col && (i = 0);
			focus(j), $inputE.val(list.at(-1));

			e.preventDefault();
		});
	$enableMask.on('input', () => {
		const enabledMask = $enableMask[0].checked;
		for (let i = 0; i < guesses.length; ++i)
			for (let j = 0; j < col; ++j) {
				if (enabledMask) {
					$guesses[i][j].firstChild.innerHTML =
						$guesses[i][j].classList.contains('chord-type-correct') ? '🟢' :
							$guesses[i][j].classList.contains('chord-type-present') ? '🟠' : '🟣';
				} else
					$guesses[i][j].firstChild.innerHTML = guesses[i][j].toHTML(puzzle.tonality);
			}
		resizeChords();
	});
}

function stop(t) {
	if (t !== true) {
		assert(transport.state !== 'stopped');
		transport.stop(typeof t === 'number' ? Math.max(Tone.now(), t) : Tone.now()).clear(schid).clear(schme);
	}

	$guesses[playState][0].parentNode.classList.remove('active');
	assert($guesses[playState][0].previousSibling.firstChild.firstChild.classList.replace('stop', 'play'));
}

async function play(chords, containsSong = false) {
	if (containsSong && duration == null) {
		$musicLoading.transition('shake');
		stop(true);
		return playState = -1;
	}

	await Tone.loaded();

	let schpl = false, tof, latency = 0;
	if (0 <= frontendConfig.chordle?.latency && frontendConfig.chordle?.latency <= 60)
		latency = frontendConfig.chordle.latency;

	function renderProgressLine() {
		const ratio = (transport.seconds - tof) * invDuration;
		$progressLine.toggleClass(['pending'], ratio < 0);
		$progressLine.css('left', 50 + Math.max(ratio, 0) * ($bars.outerWidth() - 50));
		if (ratio > 1 || !~playState || transport.state === 'stopped') schpl = false, $progressLine.hide();
		else schpl = true, requestAnimationFrame(renderProgressLine);
	}

	let _1 = puzzle.beatOffset;
	function metronome(t) { _1 < puzzle.beats && metronomes.player(beatPattern(n, _1++)).start(t); }

	const
		[n, d] = puzzle.timeSignature, beat = 60 / puzzle.bpm,
		invDuration = d / (4 * beat * puzzle.beats), $progressLine = $('.progress-line');

	syncedSynth._activeVoices.forEach(({ voice }) => voice.oscillator.stop());
	syncedSynth._scheduledEvents.forEach(id => transport.clear(id));
	syncedSynth._scheduledEvents.splice(0);

	let i = 0, offset = tof = containsSong ? puzzle.offset : 0;
	for (const segment of puzzle.chords) {
		const duration = beat * segment.duration;
		if (segment.chord instanceof Chord) {
			if (chords[i] instanceof Chord)
				for (const [pitch, vel] of chords[i].notes)
					syncedSynth.triggerAttackRelease(pitch, duration * .75, offset, vel);
			++i;
		}
		offset += duration;
	}
	tof += latency;
	assert(i === col);

	player?._scheduled.forEach(id => transport.clear(id));
	player?._scheduled.splice(0);
	if (containsSong)
		player.start(0);
	else
		schme = transport.scheduleRepeat(metronome, beat * 4 / d, 0);

	schid = transport.start().scheduleOnce(
		t => { stop(t); playState = -1; },
		(containsSong ? duration : 1 / invDuration) + latency
	);

	$progressLine.show();
	if (!schpl) renderProgressLine();
}

function latencyTest() {
	if (~playState) stop();
	playState = -1;

	syncedSynth._activeVoices.forEach(({ voice }) => voice.oscillator.stop());
	syncedSynth._scheduledEvents.forEach(id => transport.clear(id));
	syncedSynth._scheduledEvents.splice(0);

	player._scheduled.forEach(id => transport.clear(id));
	player._scheduled.splice(0);

	let _1 = 0;
	function metronome(t) { metronomes.player(beatPattern(4, _1++)).start(t); }

	schme = transport.scheduleRepeat(metronome, .25, 0);
	transport.start();
}

function latencyTestEnd() {
	assert(transport.state !== 'stopped');
	transport.stop().clear(schme);
}

document.addEventListener('DOMContentLoaded', async () => {
	await bootstrap({
		initUser: {
			requireSign: true
		}
	});

	try {
		await initPuzzle();
		initSynth();
		renderMetadata();
		renderDashboard();
		gameHandler();
		if (!frontendConfig.chordle?.disableHelper) {
			interfaces.helper.show();
			if (frontendConfig.chordle?.disableHelper == null)
				setAndSaveConfig(['chordle', 'disableHelper'], true);
		}
	} catch (e) {
		$('.ui.bottom.attached.segment').hide();
		$('.ui.bottom.attached.error.message').show().children('.content').append(
			$('<ul class="list">').append(err2lines(e).map(entry => $('<li>').text(entry)))
		);
	}
});
