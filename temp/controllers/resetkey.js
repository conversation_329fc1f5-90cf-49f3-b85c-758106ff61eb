import { <PERSON><PERSON><PERSON> } from '../buffer.js';
import Form from '../form/Form.js';
import ResetKeyForm from '../form/ResetKeyForm.js';
import Modals from '../modal/Modals.js';
import { post, bootstrap } from '../util/web.js';

let $form;

async function resetkey(config) {
	const response = await post('/resetkey', {
		uid: config.uid,
		email: config.email,
		verifyCode: config['email-verify'],
		modulus: Buffer.fromBigInt(BigInt(config.modulus)).asBase64(),
		exponent: Number(config.exponent)
	});
	if (response.status === 'ok') {
		if (await Modals.resetSucc().show() === true) return location.assign('/login');
	} else {
		ResetKeyForm.extraError($form, response.reason);
	}
}

function initVC() {
	const
		$uid = $form.form('get field', 'uid'),
		$email = $form.form('get field', 'email');

	$('.ui.email-verify-code.button').on('click', async () => {
		const
			uid = $uid.val().trim(),
			email = $email.val().trim();
		if (!($form.form('validate field', 'uid') && $form.form('validate field', 'email'))) return;
		Form.chore($form);
		const response = await post('/resetkey/email-verify', { uid, email });
		if (response.status === 'ok') {
			Modals.vcSuccess().show();
		} else {
			ResetKeyForm.extraError($form, response.reason);
		}
	})
}

document.addEventListener('DOMContentLoaded', async () => {
	await bootstrap();

	$form = $('.ui.form');
	$form.form(new ResetKeyForm().addCallback(resetkey).build($form));

	initVC();
});
