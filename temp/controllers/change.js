import { Buffer } from '../buffer.js';
import Counter from '../counter.js';
import ChangeForm from '../form/ChangeForm.js';
import Modals from '../modal/Modals.js';
import { preloadEditor, loadEditor, renderEditor, dropDownBasicConfig } from '../render.js';
import User from '../user.js';
import { safeParse, sortedStringify, assert, isEmptyObject, PTSDecomposition, checkIntRange } from '../util/type.js';
import { authPost, setConfig, staticResolve, bootstrap } from '../util/web.js';

// globals
let editor = {}, lastConfig, emailList;
// $elements
let $form, $demo, $hostLimit;
// functions
let renderEmailTable, addToEmailTable, validateEmailTable;

const
	candidateCDNs = [
		"https://cdnjs.cloudflare.com/ajax/libs/",
		"https://ajax.googleapis.com/ajax/libs/",
		"https://cdn.bootcdn.net/ajax/libs/",
		"https://cdn.staticfile.org/",
		"https://cdnjs.cat.net/ajax/libs/",
		"https://cdnjs.loli.net/ajax/libs/",
		"https://libs.cdnjs.net/",
		"/ajax/libs/"
	];

async function change(config) {
	if (!validateEmailTable()) {
		$hostLimit.transition('shake');
		$form.form('add errors', [$hostLimit.text()]);
		return;
	}
	const al = JSON.stringify(lastConfig).length;
	if (al > 16384) {
		$form.form('add errors', [`Ajax libs CDN 配置不得超过 16384 个字符 (不含空白符，现已 <span style="color: fuchsia">${al}</span> 个字符)`]);
		return;
	}
	function getEmailChanges() {
		const li = [];
		for (const entry of emailList) {
			const p = entry.element.find('input[type="checkbox"]')[0].checked;
			if (Object.hasOwn(entry, 'verifyCode')) { // new entry
				if (entry.enabled)
					li.push({ type: '+', address: entry.address, public: p, verifyCode: entry.verifyCode });
			} else { // old entry
				li.push({ type: entry.enabled ? '+' : '-', address: entry.address, public: p });
			}
		}
		return li;
	}

	const
		me = User.current,
		changedAuth = !(
			BigInt(config.modulus) === me.modulus && BigInt(config.exponent) === me.exponent
		),
		ec = getEmailChanges(),
		oldAjax = sortedStringify(me.ajax),
		newAjax = sortedStringify(lastConfig),
		changes = {};

	if (changedAuth) {
		changes.auth = {
			modulus: Buffer.fromBigInt(BigInt(config.modulus)).asBase64(),
			exponent: Number(config.exponent)
		};
	}

	if (!isEmptyObject(ec)) {
		changes.emails = ec;
	}

	if (oldAjax !== newAjax) {
		if (await Modals.changeAjax().show() !== true) return;
		changes.ajax = lastConfig;
	}

	const response = await authPost('/change', changes);
	if (response.status === 'ok') {
		if (changedAuth) {
			if (await Modals.changeToLogin().show() === true) return location.assign('/login');
		} else {
			await Modals.changeSucc().show();
		}
		location.reload();
	} else {
		ChangeForm.extraError($form, response.reason);
	}
}

async function initEmail() {
	const
		$buttonVerifyCode = $('.ui.email-verify-code.button'),
		$buttonAddEmail = $('.ui.add-email.button'),
		$email = $form.form('get field', 'email'),
		$emailVerify = $form.form('get field', 'email-verify'),
		locale = { disable: '删除', enable: '恢复' };

	// TODO: numbered table class
	{
		const
			$table = $('.ui.email.table'),
			$tbody = $table.find('tbody'),
			$none = $tbody.children(),
			$check = $('<div class="ui fitted toggle checkbox"><input type="checkbox" /><label>&nbsp;</label></div>'),
			toggleEmail = function () {
				const
					$row = $(this).closest('tr'),
					idx = $row.index();
				try {
					assert(
						$row[0].firstChild.textContent === (idx + 1).toString() &&
						0 <= idx && idx < emailList.length
					);
					const entry = emailList[idx];
					if (entry.enabled = !entry.enabled) {
						$row.removeClass(['disabled']);
						this.textContent = locale.disable;
						updateEDS(entry.domain, 1);
					} else {
						$row.addClass(['disabled']);
						this.textContent = locale.enable;
						updateEDS(entry.domain, -1);
					}
				} catch {
					alert('好像有什么不对劲的 ...');
				}
			},
			updateEDS = function (domain, delta) {
				const
					v = counter.update(domain, delta),
					affected = emailList.filter(entry => entry.domain === domain);
				if (v > 1) {
					affected.forEach(entry => entry.element.toggleClass(['error'], entry.enabled));
				} else {
					affected.forEach(entry => entry.element.removeClass(['error']));
				}
			},
			buildElement = function (entry, idx) {
				const
					$button = $(`<button class="ui button" style="margin-right: 0">${entry.enabled ? locale.disable : locale.enable}</button>`).on('click', toggleEmail),
					$row = $(entry.enabled ? (counter.get(entry.domain) > 1 ? '<tr class="error">' : '<tr>') : '<tr class="disabled">')
						.append(
							`<td>${idx + 1}</td><td>${entry.address}</td><td>${entry.extra}</td>`,
							$('<td>').append((c => {
								const $e = $check.clone();
								$e[0].firstChild.checked = c;
								return $e;
							})(entry.public)),
							$('<td>').append($button)
						);
				return entry.element = $row;
			};
		let counter;

		renderEmailTable = function () {
			counter = new Counter();
			if (!emailList.length) return;
			emailList.forEach(entry => entry.enabled && counter.update(entry.domain));
			$tbody.empty().append(emailList.map(buildElement));
		}

		addToEmailTable = function (entry) {
			buildElement(entry, emailList.push(entry) - 1);
			$none.detach();
			$tbody.append(entry.element);
			updateEDS(entry.domain, 1);
		}

		validateEmailTable = function () {
			return [...counter.values()].every(x => x <= 1);
		}
	}

	$hostLimit = $('.ui.email-warning.message > ul > li:first-child');

	await User.current.handleEmails();
	emailList = User.current.emails.map(entry => ({
		...entry,
		username: entry.address.split('@')[0],
		domain: entry.address.split('@')[1],
		element: null,
		enabled: true
	}));

	renderEmailTable();

	$buttonVerifyCode.on('click', async () => {
		const email = $email.val().trim();
		if (!(email && $form.form('validate field', 'email'))) return;
		ChangeForm.chore($form);
		const response = await authPost('/email-verify', { email });
		if (response.status === 'ok') {
			Modals.vcSuccess().show();
		} else {
			ChangeForm.extraError($form, response.reason);
		}
	});

	$buttonAddEmail.on('click', async () => {
		const
			address = $email.val().trim(),
			verifyCode = $emailVerify.val().trim();
		if (!(
			address && $form.form('validate field', 'email') &&
			verifyCode && $form.form('validate field', 'email-verify')
		)) return;
		const newEmail = {
			address,
			extra: `新项目: 验证码为 ${verifyCode}`,
			public: true,
			username: address.split('@')[0],
			domain: address.split('@')[1],
			element: null,
			enabled: true,
			verifyCode
		};
		addToEmailTable(newEmail);
	});
}

function initDropdown() {
	$form.form('get field', 'maxAge').onInput(updateJson);
	$form.form('get field', 'maxAgeTransit').onInput(updateJson);
	$form.form('get field', 'intIJQuery').onInput(updateJson);

	['base', 'css', 'js', 'mjs'].forEach(key =>
		$(`input[name="ajax.${key}"]`).parent().dropdown({
			...dropDownBasicConfig,
			allowAdditions: true,
			values: candidateCDNs.map(cdn => ({ name: cdn, value: cdn })),
			onChange: updateJson
		})
	);
	$('input[name="ajaxIJQuery"]').parent().dropdown({
		...dropDownBasicConfig,
		allowAdditions: true,
		values: ['jquery/3.6.0/jquery.min.js', '#/ajax/libs/jquery/3.6.0/jquery.min.js'].map(cdn => ({ name: cdn, value: cdn })),
		onChange: updateJson
	});

	$demo = [
		$('.ajax-css-semantic-result')[0],
		$('.ajax-js-jquery-result')[0],
		$('.ajax-mjs-index-result')[0]
	];
}

function updateJson() {
	if (!editor.$unlock) return;
	editor.$removeBlank = false;

	let config, flag;
	const value = editor.getValue();

	[config, flag] = safeParse(value);
	if (!flag) config = lastConfig;

	const
		_1 = $form.form('get values', ['maxAge', 'maxAgeTransit', 'ajax.base', 'ajax.css', 'ajax.js', 'ajax.mjs', 'ajaxIJQuery', 'intIJQuery']),
		_2 = x => x && checkIntRange(x = Number(x), 0, 31536e3) ? x : null,
		_3 = x => x.toLowerCase() === 'true' ? true : x.toLowerCase() === 'false' ? false : x || null;

	setConfig(['maxAge'], _2(_1.maxAge), config);
	setConfig(['maxAgeTransit'], _2(_1.maxAgeTransit), config);
	setConfig(['generic', 'base'], _1['ajax.base'] || null, config);
	setConfig(['generic', 'css'], _1['ajax.css'] || null, config);
	setConfig(['generic', 'js'], _1['ajax.js'] || null, config);
	setConfig(['generic', 'mjs'], _1['ajax.mjs'] || null, config);
	setConfig(['resources', 'js', 'jquery'], _1.ajaxIJQuery || null, config);
	setConfig(['integrity', 'js', 'jquery'], _3(_1.intIJQuery), config);

	const
		newValue = sortedStringify(config, '\t'),
		[prefix, suffix] = PTSDecomposition(value, newValue),
		P = editor.getModel().getPositionAt(prefix),
		S = editor.getModel().getPositionAt(value.length - suffix),
		range = new monaco.Range(P.lineNumber, P.column, S.lineNumber, S.column);
	editor.executeEdits('', [{
		forceMoveMarkers: true,
		range,
		text: newValue.substring(prefix, newValue.length - suffix)
	}]);
	editor.$removeBlank = true;
}

function jsonToTest(config) {
	lastConfig = config;

	if (editor.$removeBlank) {
		editor.$unlock = false;
		let v = config.maxAge, o = {};
		if (checkIntRange(v, 0, 31536e3)) o.maxAge = v;
		v = config.maxAgeTransit;
		if (checkIntRange(v, 0, 31536e3)) o.maxAgeTransit = v;
		['base', 'css', 'js', 'mjs'].forEach(key => {
			v = config.generic?.[key];
			if (v === '') $form.form('get field', `ajax.${key}`).parent().dropdown('clear');
			else if (typeof v === 'string') o[`ajax.${key}`] = v;
		});
		v = config.resources?.js?.jquery;
		if (v === '') $form.form('get field', 'ajaxIJQuery').parent().dropdown('clear');
		else if (typeof v === 'string') o.ajaxIJQuery = v;
		v = config.integrity?.js?.jquery;
		if (typeof v === 'string' || typeof v === 'boolean') o.intIJQuery = v;
		$form.form('set values', o);
		editor.$unlock = true;
	}

	$demo[0].href = $demo[0].textContent = staticResolve(config, 'css', 'semantic', 'semantic-ui/2.4.1/semantic.min.css');
	$demo[1].href = $demo[1].textContent = staticResolve(config, 'js', 'jquery', 'jquery/3.6.0/jquery.min.js');
	$demo[2].href = $demo[2].textContent = staticResolve(config, 'mjs', 'index', '#/js/controllers/index.js');
}

preloadEditor();
document.addEventListener('DOMContentLoaded', async () => {
	await bootstrap({
		initUser: {
			fields: ['ajax', 'emails'],
			requireSign: true
		}
	});

	$form = $('.ui.form');
	if (!User.current) return $form.hide(), $('.ui.attached.placeholder.segment').show();
	$form.form(new ChangeForm().addCallback(change).build($form)).form('set values', User.current);

	lastConfig = User.current.ajax;
	initEmail();
	initDropdown();

	await loadEditor();
	editor = renderEditor({
		language: 'json',
		value: sortedStringify(lastConfig, '\t')
	});
	editor.$unlock = editor.$removeBlank = true;
	editor.onDidChangeModelContent(() => {
		const [obj, flag] = safeParse(editor.getValue());
		if (flag) jsonToTest(obj);
	});
	jsonToTest(lastConfig);
});
