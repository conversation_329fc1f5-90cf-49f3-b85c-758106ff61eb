import CRTSignFuncForm from '../form/CRTSignFuncForm.js';
import LoginForm from '../form/LoginForm.js';
import SimpleSignFuncForm from '../form/SimpleSignFuncForm.js';
import Modals from '../modal/Modals.js';
import { renderHead, preloadEditor, loadEditor, renderEditor } from '../render.js';
import User from '../user.js';
import { stripLeadingZero as slz, err2lines } from '../util/type.js';
import { bootstrap } from '../util/web.js';

// globlals
let editor = {};
// $elements
let $form, $menu, $outcome;

const
	simpleSignFuncGen = d => `const
	User = (await import('/js/user.js')).default,
	{ PowerMod } = await import('/js/util/nt.js'),
	n = User.current.modulus,
	d = ${slz(d)}n;

return function (data) {
	return PowerMod(data % n, d, n); // ~ 35 ms
}
`,
	crtSignFuncGen = ({ d, p, q, dp, dq, qi }) => `const
	User = (await import('/js/user.js')).default,
	{ PowerMod } = await import('/js/util/nt.js'),
	n = User.current.modulus,
	d = ${slz(d)}n,
	p = ${slz(p)}n,
	q = ${slz(q)}n,
	dp = ${slz(dp)}n,
	dq = ${slz(dq)}n,
	qi = ${slz(qi)}n;

if (! (n === p * q && dp === d % (p - 1n) && dq === d % (q - 1n)) ) {
	throw new Error('basic condition violated');
}

return function (data) {
	const sign_p = PowerMod(data % p, dp, p), sign_q = PowerMod(data % q, dq, q);
	return (sign_p - sign_q % p + p) * qi % p * q + sign_q; // ~ 10 ms
}
`;

function manifest(data, clazz = 'error') {
	$outcome
		.removeClass(['success', 'warning', 'error'])
		.addClass([clazz])
		.empty()
		.append(
			$('<ul class="list">').append(
				Array.isArray(data) ? data.map(entry => $('<li>').text(entry)) : data
			)
		);
	editor.layout?.();
}

async function login(config) {
	const { uid } = config;
	if (uid) {
		const user = await User.byUID(uid);
		if (!user) return $form.form('add prompt', 'uid', `UID ${uid} 不存在，<a href="/register">点此</a>注册`);
		$form.form('set values', User.current = user);
	} else {
		$form.form('set values', { modulus: '', exponent: '' });
		User.current = null;
	}
	User.updateStorages();
	renderHead();
}

function initSignArea() {
	$menu = $('.ui.sign-choice.menu');
	const
		$simpleTab = $menu.tab('get tab element', 'simple'),
		$crtTab = $menu.tab('get tab element', 'crt'),
		$customTab = $menu.tab('get tab element', 'custom'),
		$uploadInput = $('#upload-sign-script'),
		setValue = text => { editor.$switchTab = false; editor.setValue(text); editor.$switchTab = true; };

	$menu.children().tab({ onVisible: () => editor.layout?.() }).first().tab('change tab', 'simple');

	$simpleTab.form(
		new SimpleSignFuncForm()
			.addCallback(config => setValue(simpleSignFuncGen(config.d)))
			.build($simpleTab)
	);
	$crtTab.form(
		new CRTSignFuncForm()
			.addCallback(config => setValue(crtSignFuncGen(config)))
			.extraLaTeX()
			.build($crtTab)
	);
	$customTab.find('.ui.upload.button').on('click', () => $uploadInput.trigger('click'));

	$uploadInput.on('input', async function () {
		if (!this.files.length) return;
		const file = this.files[0];
		this.value = '';
		setValue(await file.text());
	});
}

function initValidator() {
	$outcome = $('.ui.outcome.message');
	const
		$validate = $('.ui.validate.button'),
		furtherCheck = async () => {
			const result = await User.current.initSign();
			if (result !== true) return manifest(err2lines(result));
			if (typeof User.signFunc !== 'function') return manifest(['未返回函数']);
			const cResult = await User.current.preCheckSign(10);
			if (cResult === false) return manifest(['无效签名']);
			if (typeof cResult === 'number') {
				const TLE = cResult >= 50;
				return manifest(
					`<li>验证通过！</li><li>调用平均耗时:&ensp;${cResult.toFixed(2)}&thinsp;ms <strong>(检验共进行 10 轮，此数值为平均耗时)</strong></li>${TLE ? `<li>注：检测到调用耗时较长 (超过 50&thinsp;ms)，以下为优化建议：<ul style="margin-top: .3rem; padding-left: 1rem"><li>提升代码效率，如开启 CRT 优化；</li><li>适当减小密钥长度，建议长度为 3072 位；</li><li>使用更高效的浏览器及内核，如 <a href="https://www.google.cn/chrome/" target="_blank">Google Chrome 最新版</a>。</li></ul></li>` : ''}`,
					TLE ? 'warning' : 'success'
				);
			}
			return manifest(err2lines(cResult));
		}
	$validate.on('click', async () => {
		if (!User.current) return manifest(['请先登录']);
		if (monaco.editor.getModelMarkers({ owner: 'javascript', resource: editor.getModel().uri }).length) {
			if (await Modals.forceSyntax().show() !== true)
				return manifest(['您的代码可能存在语法错误，已取消进一步检验。']);
		}
		await furtherCheck();
	});
}

preloadEditor();
document.addEventListener('DOMContentLoaded', async () => {
	await bootstrap();

	$form = $('.ui.attached.form');
	if (User.current) $form.form('set values', User.current);
	$form.form(new LoginForm().addCallback(login).build($form));

	initSignArea();
	initValidator();

	await loadEditor();
	editor = renderEditor({
		language: 'javascript',
		value: localStorage.getItem('signFunc')
	});
	editor.$switchTab = true;
	editor.onDidChangeModelContent(() => {
		if (editor.$switchTab) {
			const $tab = $menu.find('.' + jQuery.fn.tab.settings.className.active);
			if ($tab.tab('get tab') !== 'custom') {
				$tab.tab('change tab', 'custom');
			}
		}
		const value = editor.getValue();
		value ? localStorage.setItem('signFunc', value) : localStorage.removeItem('signFunc');
		$outcome.removeClass(['success', 'warning', 'error']);
	});
});
