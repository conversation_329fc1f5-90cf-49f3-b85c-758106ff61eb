import Pagination from '../../Pagination.js';
import User from '../../user.js';
import { deAES } from '../../util/crypto.js';
import { checkIntRange, date2datestr } from '../../util/type.js';
import { frontendConfig, post, bootstrap } from '../../util/web.js';

// globals
let todayId, pageLength = 50, cachedPage = new Map();

function julian2str(julian) { return date2datestr(new Date((julian - 2440587) * 864e5), true); }
function avg(data) { return (data.sum / data.count).toFixed(1).replaceAll('.0', ''); }

async function initPuzzleList() {
	let response;

	if (User.current) {
		const
			raw = await post('/chordle/data', { uid: User.current.uid, }),
			json = (await deAES(raw.result, User.current)).asUtf8();
		response = JSON.parse(json);
	} else
		response = (await post('/chordle/data', {})).result;

	todayId = response.todayId;

	const
		$tbody = $('.ui.table>tbody'),
		$loading = $tbody.children().detach(),
		$pagination = $('.ui.pagination.menu'),
		pl = frontendConfig.chordle?.pageLength;
	if (checkIntRange(pl, 10, 500)) pageLength = pl;

	$('head').append('<link rel="icon" type="images/png" href="/attachment/chordle/icon.png">');
	if (todayId <= 0) return $tbody.lazyEmpty().append('<tr class="chordle-list-none"><td class="center aligned" colspan="9">无</td></tr>');

	function renderPage(curPage) {
		if (cachedPage.has(curPage)) return $tbody.lazyEmpty().append(cachedPage.get(curPage));

		// loading
		if ($loading.is(':visible')) return;

		$tbody.lazyEmpty().append($loading);
		getPageContent(curPage).then(content => {
			cachedPage.set(curPage, content);
			$tbody.lazyEmpty().append(content);
		});
	}

	new Pagination(Math.ceil(todayId / pageLength), renderPage, $pagination).build();
}

async function getPageContent(page) {
	const l = Math.max(todayId - page * pageLength, 0) + 1, r = todayId - (page - 1) * pageLength;
	let response;

	// this feature is devised for the Chordle admin, it will run without authentication (if not admin) then.
	if (User.current) {
		const
			raw = await post('/chordle/data', { uid: User.current.uid, listData: [l, r] }),
			json = (await deAES(raw.result, User.current)).asUtf8();
		response = JSON.parse(json);
	} else
		response = (await post('/chordle/data', { listData: [l, r] })).result;

	return response.batch.map(renderRow).reverse();
}

function renderRow(puzzle) {
	const
		_1 = {
			major: ['C', 'D♭', 'D', 'E♭', 'E', 'F', 'G♭', 'G', 'A♭', 'A', 'B♭', 'B'],
			minor: ['C', 'C♯', 'D', 'E♭', 'E', 'F', 'F♯', 'G', 'G♯', 'A', 'B♭', 'B']
		},
		_2 = { major: '大调', minor: '小调' },
		link = '/chordle' + (puzzle.id === todayId ? '' : `?id=${puzzle.id}&force=true`),
		// $lock = $('<td>').html('<i class="red lock icon">'),
		$id = $('<td>').html(`<a href="${link}">${puzzle.id}</a>`),
		$startDate = $('<td>').text(julian2str(puzzle.startDate)),
		$bpm = $('<td>').text('♩= ' + puzzle.data.bpm),
		$tonality = $('<td>').html(`<span class="chord-root">${_1[puzzle.data.tonality.scale][puzzle.data.tonality.tonic]}</span> <span class="tonality-scale ${puzzle.data.tonality.scale}">${_2[puzzle.data.tonality.scale]}</span>`),
		$timeSignature = $('<td>').text(puzzle.data.timeSignature.join('/')),
		$then = $('<td>').text(puzzle.thenStat.count ? `${puzzle.thenStat.count} (${avg(puzzle.thenStat)})` : 0),
		$now = $('<td>').text(puzzle.nowStat.count ? `${puzzle.nowStat.count} (${avg(puzzle.nowStat)})` : 0)

	let $row;

	if (puzzle.win)
		$row = [$('<td>').html(`<i class="${puzzle.win === 1 ? 'yellow winner' : 'green check'} icon">`), $id, $('<td>').append($(`<a href="${link}">`).text(puzzle.data.title)), $startDate, $bpm, $tonality, $timeSignature, $then, $now];
	else
		$row = [$('<td>'), $id, $('<td>').html(`<a href="${link}"><i class="red lock icon"></i></a>`), $startDate, $bpm, $tonality, $timeSignature, $then, $now];

	return $(puzzle.win === 1 ? '<tr class="positive">' : '<tr>').append($row);
}

document.addEventListener('DOMContentLoaded', async () => {
	await bootstrap({
		initUser: {
			requireSign: true
		}
	});

	initPuzzleList();
});
