import { Buffer } from '../buffer.js';
import Modals from '../modal/Modals.js';
import { preloadEditor, loadEditor, renderEditor } from '../render.js';
import User from '../user.js';
import { enAES, deAES } from '../util/crypto.js';
import { safeParse, sortedStringify, date2str } from '../util/type.js';
import { authPost, bootstrap } from '../util/web.js';

// globals
let editor = {}, lastConfig;
let remoteAvailable, remoteConfig, lastModified, lastModifiedRemote;
// $elements
let $lastModified, $lastModifiedRemote;

function setLastModified(value) {
	localStorage.setItem('preferencesLastModified', (lastModified = value).getTime());
}

function renderTime() {
	$lastModified.text(date2str(lastModified));
	if (remoteAvailable) {
		$lastModifiedRemote.text(lastModifiedRemote > 0 ? date2str(lastModifiedRemote) : '无记录');
		if (lastModified < lastModifiedRemote) {
			$lastModified.css('color', 'red');
			$lastModifiedRemote.css('color', '#0c0');
		} else {
			$lastModified.css('color', '#0c0');
			$lastModifiedRemote.css('color', lastModified.getTime() === lastModifiedRemote.getTime() ? '#0c0' : 'red');
		}
	} else if (User.current) {
		$lastModifiedRemote.text('身份认证未通过');
		$lastModified.css('color', '#0c0');
		$lastModifiedRemote.css('color', 'red');
	} else {
		$lastModifiedRemote.text('未登录');
		$lastModified.css('color', '#0c0');
		$lastModifiedRemote.css('color', 'red');
	}
}

async function fetchRemote() {
	try {
		const
			raw = await authPost('/preference-sync', {}),
			json = (await deAES(raw.result, User.current)).asUtf8();
		({ preference: remoteConfig, lastModified: lastModifiedRemote } = JSON.parse(json));
		lastModifiedRemote = new Date(lastModifiedRemote);
	} catch (e) {
		remoteConfig = {};
		lastModifiedRemote = new Date(0);
	}
}

async function initSync() {
	$lastModifiedRemote = $('.last-modified-remote');
	const
		$upload = $('.ui.upload.button'),
		$download = $('.ui.download.button'),
		$sync = $('.ui.sync.button');

	async function upload(fetch) {
		if (fetch) {
			if (!remoteAvailable) {
				if (await Modals.reconfigSignFunc().show() === true) return location.assign('/login');
			}
			await fetchRemote();
		}
		if (lastModified < lastModifiedRemote) {
			if (await Modals.wrongUpload().show() !== true) return;
		}

		const json = sortedStringify({ preference: lastConfig, lastModified: lastModified.getTime() });

		await authPost(
			'/preference-sync',
			await enAES(Buffer.fromUtf8(json), User.current)
		);

		lastModifiedRemote = lastModified;
		renderTime();
		$download.removeClass(['disabled']);
	}

	async function download(fetch) {
		if (fetch) {
			if (!remoteAvailable) {
				if (await Modals.reconfigSignFunc().show() === true) return location.assign('/login');
			}
			await fetchRemote();
		}
		if (!(lastModifiedRemote > 0)) return;
		if (!(lastModified <= lastModifiedRemote)) {
			if (await Modals.wrongDownload().show() !== true) return;
		}

		setLastModified(lastModifiedRemote);
		renderTime();

		editor.$manual = false;
		editor.setValue(sortedStringify(remoteConfig, '\t'));
		editor.$manual = true;
	}

	async function sync() {
		if (!remoteAvailable) {
			if (await Modals.reconfigSignFunc().show() === true) return location.assign('/login');
		}
		await fetchRemote();
		if (lastModified < lastModifiedRemote) await download(false);
		else if (lastModifiedRemote < lastModified) await upload(false);
	}

	if (!User.current) $([$upload[0], $download[0], $sync[0]]).addClass(['disabled']);
	if (remoteAvailable = User.current && Modals.reconfigSignFunc().sfError == null) {
		await fetchRemote();
		lastModifiedRemote > 0 ? $download.removeClass(['disabled']) : $download.addClass(['disabled']);
	}

	renderTime();
	$upload.on('click', () => upload(true));
	$download.on('click', () => download(true));
	$sync.on('click', sync);
}

function jsonToTest(config) {
	lastConfig = config;
	localStorage.setItem('preferences', sortedStringify(lastConfig));

	if (editor.$manual) {
		setLastModified(new Date());
		renderTime();
	}
}

preloadEditor();
document.addEventListener('DOMContentLoaded', async () => {
	await bootstrap();

	lastConfig = safeParse(localStorage.getItem('preferences'))[0];
	const x = localStorage.getItem('preferencesLastModified');
	if (x == null || isNaN(x) || isNaN(lastModified = new Date(Number(x)))) lastModified = new Date(1);
	$lastModified = $('.last-modified');

	initSync();

	await loadEditor();
	editor = renderEditor({
		language: 'json',
		value: sortedStringify(lastConfig, '\t'),
	});
	editor.onDidChangeModelContent(() => {
		const [obj, flag] = safeParse(editor.getValue());
		if (flag) jsonToTest(obj);
	});
	jsonToTest(lastConfig);
	editor.$manual = true;
});
