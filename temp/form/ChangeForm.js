import RegisterForm from './RegisterForm.js';
import EmailField from './field/EmailField.js';
import VerifyCodeField from './field/VerifyCodeField.js';

export default class ChangeForm extends RegisterForm {
	static errTable = {
		...super.errTable,
		'email-verify': {
			invalid: t => `邮箱 ${t} 的验证码无效`,
			tooFrequently: t => `验证码发送过于频繁，请等待 ${t} 秒后再次重试`
		}
	}

	constructor() {
		super()
			.addField(new EmailField('email', '邮箱').setOptional())
			.addField(new VerifyCodeField('email-verify', '邮箱验证码').setOptional());
	}
}
