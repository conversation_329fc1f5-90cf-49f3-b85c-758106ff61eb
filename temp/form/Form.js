import renderKaTeX from '/ajax/transit/render-katex';

export default class Form {
	static errTable = {
		'': {
			dataContainsIllegalChar: '身份验证失败：数据包含非法字符',
			invalidSign: '身份验证失败：无效签名',
			signFormatError: '身份验证失败：签名格式错误'
		},
	}

	build($form) {
		const that = this;
		return {
			inline: this.inline,
			fields: Object.fromEntries(this.fields.map(field => [field.name, field.build()])),
			onSuccess: function (_event, fields) {
				Form.chore($form);
				that.callbacks.forEach(callback => callback.call(this, fields));
			},
			onInvalid: function () {
				that.invalidCallbacks.forEach(callback => callback.call(this));
				const $prompts = $form.find(jQuery.fn.form.settings.selector.prompt);
				if ($prompts.length) $prompts.first().closest(jQuery.fn.form.settings.selector.group)[0]?.scrollIntoView?.({ behavior: 'smooth', block: 'nearest' });
			}
		}
	}

	static extraError($form, reason) {
		const { field, type, arguments: args } = reason,
			message = this.errTable?.[field]?.[type] ?? this.errTable?.[type] ?? type,
			prompt = typeof message === 'function' ? message(...args) : Array.isArray(message) ? message : message?.toString();
		if (field) {
			$form.form('add prompt', field, prompt);
			$form.form('get field', field).closest(jQuery.fn.form.settings.selector.group)[0]?.scrollIntoView?.({ behavior: 'smooth', block: 'nearest' });
		} else {
			$form.form('add errors', Array.isArray(prompt) ? prompt : [prompt]);
		}
	}
}
