import { assert } from '../util/type.js';
import type Stacker from './Stacker.js';

export default abstract class Stack {
	$breadcrumb: JQuery | null = null;
	$content: jqNode | jqNode[] = null;
	stacker: Stacker | null = null;
	abstract breadcrumbName: string;

	breadcrumbNavigation() {
		assert(this.stacker);
		const $breadcrumb = this.stacker.breadcrumbNavigation();
		if (this.$breadcrumb) {
			this.$breadcrumb.replaceWith($breadcrumb);
		}
		return this.$breadcrumb = $breadcrumb;
	}

	getBreadcrumbItem(index: number, activeIndex: number) {
		if (index === activeIndex) {
			return $('<div class="active section">').text(this.breadcrumbName);
		}
		return $('<a class="section">').text(this.breadcrumbName).on('click', () => {
			assert(this.stacker);
			this.stacker.goto(index).render();
		});
	}

	abstract getHeader(): jqNode | jqNode[];

	abstract getFirstContent(): jqNode | jqNode[];

	getContent(): jqNode | jqNode[] {
		if (this.$content == null) {
			this.$content = this.getFirstContent();
		} else {
			this.refreshContent();
		}
		return this.$content!;
	}

	refreshContent() {
		this.breadcrumbNavigation();
	}
}
