export default abstract class ModalBase {
	class: string;
	inverted: boolean;
	hasCloseIcon: boolean;
	$element: JQuery | null = null;
	$header: JQuery | null = null;
	$content: JQuery | null = null;

	constructor(clazz: string) {
		this.class = clazz;
		this.inverted = true;
		this.hasCloseIcon = true;
	}

	setClass(clazz: string) {
		this.class = clazz;
		return this;
	}

	abstract firstRender(): void;

	render() {
		// do nothing.
	}

	show() {
		if (!this.$element) {
			this.$element = $(`<div class="${this.class}">`).append(
				this.hasCloseIcon ? $('<i class="close icon">') : null,
				this.$header = $('<div class="header" style="font-size: 1.42857143em">'),
				this.$content = $('<div class="content">')
			).modal({
				inverted: this.inverted,
			});
			this.firstRender();
		}
		this.render();
		this.$element!.modal('show');
	}
}
