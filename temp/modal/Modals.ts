import { escapeHTML } from '../util/string.js';
import Modal from './Modal.js';

function _(ctor: () => Modal) {
	let instance: Modal | null = null;
	return () => instance ??= ctor();
}

const Icons = {
	ERROR: 'red x circle',
	OK: 'green check',
	QUESTION: 'pink question circle outline',
	WARNING: 'yellow warning sign',
}

export default {
	authFailed: _(() =>
		new Modal('reconfigWhenAuthenticationFailed')
			.iconHeader(Icons.ERROR, '身份验证失败')
			.withContent('<p>执行操作时身份验证失败，是否跳转到登录页重新配置签名函数？</p>')),

	changeAjax: _(() =>
		new Modal('warningWhenChangeAjaxSettings')
			.iconHeader(Icons.WARNING, 'Ajax 配置修改确认')
			.withContent('<p>检测到您修改了 Ajax 配置。这包含许多底层的配置，错误的配置很容易造成网页无法正常访问。请再次确认你的配置是合理无误的。</p><p>如果这是您的首次修改，请务必阅读以下内容：</p><div class="ui small info message"><div class="header">恢复方法</div><p>当您修改配置后发现网页出错时，可用如下方式重新配置：</p><h5 class="ui header">方法一：</h5><ul class="list"><li>打开浏览器控制台，找到 <a href="https://developer.mozilla.org/zh-CN/docs/Web/API/Window/localStorage" target="_blank">Local Storage</a> 中的 <code>preferences</code> 项 (如果没有则创建一个)，这应当一个 JSON 配置。</li><li>配置该 JSON，设置 <code>cookie.whiteList</code> 字段的值为 <code>[]</code>。(如果不理解上述含义，可以直接将整个 <code>preferences</code> 项改为 <code>{"cookie": {"whiteList": []}}</code>，不过代价是可能会丢失其余未同步的偏好设置)</li><li>回到本页面刷新并重新配置。(配置完毕后不要忘记恢复原先的偏好设置)</li></ul><h5 class="ui header" style="margin-top: .5rem">方法二：</h5><ul class="list"><li>删除名称为 <code>uid</code> 的 Cookie (可以使用各种工具如浏览器控制台删除)。</li><li>去 <a href="/preference" target="_blank">偏好设置 (/preference)</a> 页面 (现在应当可以正常访问)。</li><li>打开 “使用 Cookie 白名单” 项 (会自动保存，无需手动保存)。</li><li>回到本页面刷新并重新配置。(配置完毕后不要忘记恢复原先的偏好设置)</li></ul></div><p>是否继续操作？</p>')),

	changeSucc: _(() =>
		new Modal('changeSuccess')
			.iconHeader(Icons.OK, '用户信息修改成功！')
			.alertStyle()),

	changeToLogin: _(() =>
		new Modal('redirectToLoginWhenChangeAuth')
			.iconHeader(Icons.QUESTION, '重新配置登录信息')
			.withContent('<p>用户信息修改成功！</p><p>检测到您修改了模数和指数，是否跳转到登录页重新配置签名函数？</p>')),

	chordleLogin: _(() =>
		new Modal('requireLoginInChordle')
			.iconHeader(Icons.WARNING, '请登录后以继续 Chordle')
			.withContent('<p>检测到您尚未登录，请登录或注册后再继续游戏 (登录注册按钮在右上角)。</p>')
			.alertStyle()),

	clearSign: _(() =>
		new Modal('clearSignFunctionWhenLogout')
			.iconHeader('green logout', '登出成功！')
			.withContent('<p>是否清除签名函数？</p>')),

	reconfigSignFunc: _(() =>
		new Modal('reconfigSignFunction')
			.iconHeader(Icons.WARNING, '签名函数验证失败')
			.setContent(function () {
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				const errors: string | string[] = (<any>this).sfError;
				if (Array.isArray(errors)) {
					return '<div class="content"><p>您的签名函数无法通过预检，若继续使用可能会造成<strong>不可预料的问题</strong>。是否跳转到登录页重新配置签名函数？</p><div class="ui small error message"><div class="header">错误信息</div><ul class="list">' + errors.map(error => `<li>${escapeHTML(error)}</li>`).join('') + '</ul></div></div>';
				} else {
					return `<div class="content"><p>您的签名函数无法通过预检 <span style="color: fuchsia">(错误类型：${escapeHTML(errors)})</span>，若继续使用可能会造成<strong>不可预料的问题</strong>。是否跳转到登录页重新配置签名函数？</p></div>`;
				}
			})),

	resetSucc: _(() =>
		new Modal('resetKeySuccess')
			.iconHeader(Icons.OK, '重置密钥成功！')
			.withContent('<p>是否跳转到登录信息配置页？</p>')),

	registerSucc: _(() =>
		new Modal('registerSuccess')
			.iconHeader(Icons.OK, '注册成功！')
			.withContent('<p>是否跳转到登录信息配置页？</p>')),

	forceSyntax: _(() =>
		new Modal('forceActionWhenSyntaxError')
			.iconHeader(Icons.QUESTION, '是否继续操作？')
			.withContent('<p>检测到您的代码似乎存在语法错误，是否继续操作？</p>')),

	vcSuccess: _(() =>
		new Modal('verifyCodeSentSuccessfully')
			.iconHeader(Icons.OK, '验证码发送成功！')
			.alertStyle()),

	wrongUpload: _(() =>
		new Modal('AskForceUploadWhenRemoteIsNewer')
			.iconHeader(Icons.QUESTION, '是否强制上传')
			.withContent('检测到远程的配置较新，是否强制上传？')),

	wrongDownload: _(() =>
		new Modal('AskForceDownloadWhenLocalIsNewer')
			.iconHeader(Icons.QUESTION, '是否强制下载')
			.withContent('检测到本地的配置较新，是否强制下载？')),
};
