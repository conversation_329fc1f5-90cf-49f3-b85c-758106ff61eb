import { frontendConfig, setAndSaveConfig } from '../util/web.js';
import ModalBase from './ModalBase.js';

type StringSupplier = string | ((this: Modal) => string);

export default class Modal extends ModalBase {
	identifier: string;
	header: StringSupplier = '';
	content: StringSupplier = '';
	okName = '是';
	cancelName = '否';
	rememberChoices = '不再提示';
	actions: StringSupplier = () => `<div class="actions"><div class="ui toggle checkbox"><input type="checkbox" /><label><span data-tooltip="可以在“偏好设置”中的 modal.${this.identifier} 项来更改或取消记住选择" data-position="bottom center">${this.rememberChoices}</span></label></div><button class="ui inverted positive basic button"><i class="check icon"></i>${this.okName}</button><button class="ui inverted negative basic button"><i class="remove icon"></i>${this.cancelName}</button></div>`;
	#checkbox: HTMLInputElement | null = null;
	#promiseFulfill: ((value: boolean | null) => void) | null = null;
	#response: boolean | null = false;

	constructor(identifier: string) {
		super('ui basic modal');
		this.identifier = identifier;
	}

	setHeader(header: StringSupplier) {
		this.header = header;
		return this;
	}

	setContent(content: StringSupplier) {
		this.content = content;
		return this;
	}

	setActions(actions: StringSupplier) {
		this.actions = actions;
		return this;
	}

	iconHeader(icon: string, header: string) {
		return this.setHeader(`<div class="ui icon header"><i class="${icon} icon"></i>${header}</div>`);
	}

	withContent(content: string) {
		return this.setContent(`<div class="content">${content}</div>`);
	}

	/**
	 * set style to alert(), not confirm().
	 * @returns this
	 */
	alertStyle() {
		this.okName = '关闭';
		return this.setActions(() => `<div class="actions"><div class="ui toggle checkbox"><input type="checkbox" /><label><span data-tooltip="可以在“偏好设置”中的 modal.${this.identifier} 项来更改或取消记住选择" data-position="bottom center">${this.rememberChoices}</span></label></div><button class="ui inverted pink basic ok button"><i class="check icon"></i>${this.okName}</button></div>`);
	}

	firstRender() {
		const
			header = typeof this.header === 'function' ? this.header.call(this) : this.header.toString(),
			content = typeof this.content === 'function' ? this.content.call(this) : this.content.toString(),
			actions = typeof this.actions === 'function' ? this.actions.call(this) : this.actions.toString();
		this.$element = $(`<div class="${this.class}">${header}${content}${actions}</div>`).modal({
			onApprove: () => {
				if (this.#checkbox!.checked === true) setAndSaveConfig(['modal', this.identifier], true);
				this.#response = true;
			},
			onDeny: () => {
				if (this.#checkbox!.checked === true) setAndSaveConfig(['modal', this.identifier], false);
				this.#response = false;
			},
			onHide: () => {
				const fulfill = this.#promiseFulfill;
				this.#promiseFulfill = null;
				if (fulfill) fulfill(this.#response);
			},
			onHidden: () => {
				this.#response = null;
			}
		});
		this.#checkbox = <HTMLInputElement>this.$element!.find('input[type="checkbox"]')[0];
	}

	/**
	 * show a modal.
	 * @returns true: ok, false: cancel, null: closed
	 */
	show(): boolean | null | Promise<boolean | null> {
		const remember: boolean | null = frontendConfig.modal?.[this.identifier];
		if (remember === true || remember === false) return remember;
		else {
			this.#response = null;
			return new Promise(fulfill => {
				this.#promiseFulfill = fulfill;
				super.show();
			});
		}
	}
}
