import { assert } from '../util/type.js';
import ModalBase from './ModalBase.js';
import Stack from './Stack.js';

export default class Stacker extends ModalBase {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	[key: string]: any;
	stack: Stack[] = [];
	activeId = 0;
	#breadcrumb: JQuery;
	#breadcrumbDivider: JQuery;
	#breadcrumbRoot: JQuery;

	constructor(identifier: string) {
		super('ui large modal');
		this.#breadcrumb = $('<div class="ui breadcrumb">');
		this.#breadcrumbRoot = $(`<div class="section">${identifier}</div>`);
		this.#breadcrumbDivider = $('<i class="angle right icon divider">');
		this.clear();
	}

	clear() {
		this.stack = [];
		this.activeId = 0; // the real active element is (this.activeId - 1)
		return this;
	}

	push(stack: Stack) {
		assert(stack instanceof Stack);
		stack.stacker = this;
		if (this.activeId < this.stack.length) {
			this.stack.splice(this.activeId);
		}
		this.activeId = this.stack.push(stack);
		return this;
	}

	goto(index: number) {
		if (index < 0 || index >= this.stack.length || ++index === this.activeId) return this;
		this.activeId = index;
		return this;
	}

	breadcrumbNavigation() {
		const $elems = this.stack.flatMap(
			(stack, idx) => [this.#breadcrumbDivider.clone(), stack.getBreadcrumbItem(idx, this.activeId - 1)]
		);
		$elems.unshift(this.#breadcrumbRoot);
		return this.#breadcrumb.lazyEmpty().append($elems);
	}

	firstRender() {
		// do nothing.
	}

	render() {
		const stack = this.stack[this.activeId - 1];
		this.$header!.lazyEmpty().append(stack.getHeader());
		this.$content!.lazyEmpty().append(stack.getContent());
		this.$element!.modal('refresh');
	}
}
