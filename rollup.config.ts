import commonjs from '@rollup/plugin-commonjs';
import { nodeResolve } from '@rollup/plugin-node-resolve';
import replace from '@rollup/plugin-replace';
import { extname, relative } from 'path';
import { defineConfig } from 'rollup';
import esbuild, { minify } from 'rollup-plugin-esbuild';

import { PHONY, getRoot, Kitsune } from './build.ts';

const ROOT = getRoot();

/*************************************** manual chunks ***************************************/
/*****/	function splitRule(id) {														/*****/
/*****/		const relaPath = relative(ROOT, id);										/*****/
/*****/		if (relaPath.startsWith('src/pages/')) {									/*****/
/*****/			debugger;																/*****/
/*****/			return 'entry';															/*****/
/*****/		}																			/*****/
/*****/		if (relaPath === 'node_modules/monaco-editor/esm/vs/nls.js')				/*****/
/*****/			return 'monaco-common';													/*****/
/*****/		if (relaPath === 'node_modules/monaco-editor/esm/vs/nls.messages.js')		/*****/
/*****/			return 'monaco-common';													/*****/
/*****/		if (/^node_modules\/monaco-editor\/esm\/vs\/.+\/common\//.test(relaPath))	/*****/
/*****/			return 'monaco-common';													/*****/
/*****/		if (relaPath.startsWith('node_modules/monaco-editor/'))						/*****/
/*****/			return 'monaco';														/*****/
/*****/		if (relaPath.startsWith('src/components/editor/'))							/*****/
/*****/			return 'monaco';														/*****/
/*****/		if (relaPath.startsWith('src/components/hcaptcha/'))						/*****/
/*****/			return 'hcaptcha';														/*****/
/*****/		if (relaPath.startsWith('src/modules/hcaptcha/'))							/*****/
/*****/			return 'hcaptcha';														/*****/
/*****/		if (relaPath === 'src/util/hcaptcha.ts')									/*****/
/*****/			return 'hcaptcha';														/*****/
/*****/		if (relaPath === 'src/util/socket.ts')										/*****/
/*****/			return 'socket';														/*****/
/*****/		if (relaPath.startsWith('node_modules/react-katex/'))						/*****/
/*****/			return 'react-katex';													/*****/
/*****/		if (relaPath.includes('/leaderboard/'))										/*****/
/*****/			return 'leaderboard';													/*****/
/*****/		if (relaPath.includes('/catcoin/'))											/*****/
/*****/			return 'catcoin';														/*****/
/*****/		if (relaPath.startsWith('src/wasm/'))										/*****/
/*****/			return 'wasm';															/*****/
/*****/		return 'libs';																/*****/
/*****/	}																				/*****/
/*********************************************************************************************/

export default Promise.resolve().then(() => defineConfig({
	external: id => {
		switch (id) {
			case 'hcaptcha': return true;
			case 'katex': return true;
			case 'katex@css': return true;
			case 'monaco-editor@css': return true;
			case 'socket.io-client': return true;
			case 'prop-types': return true;
			case 'react': return true;
			case 'react-dom/client': return true;
			case 'semantic-ui-react': return true;
			case 'semantic@css': return true;
			case 'tone': return true;
		}
		if (id.startsWith('monaco-editor/'))
			return true;
		if (id.startsWith('/js/polyfill/'))
			return true;
		return false;
	},
	input: {
		index: './src/pages/index.tsx',
		logout: './src/pages/logout.ts',
		preference: './src/pages/preference.tsx',
		rot10451: './src/pages/rot10451.tsx',
		'chordle/list': './src/pages/chordle/list.tsx',
		genkey: './src/pages/genkey.tsx',
		login: './src/pages/login.tsx',
		register: './src/pages/register.tsx',
		change: './src/pages/change.tsx',
		resetkey: './src/pages/resetkey.tsx',
		chordle: './src/pages/chordle.tsx',
		leaderboard: './src/pages/LeaderBoard.tsx',
		ourtimes: './src/pages/ourtimes.tsx',
		catcoin: './src/pages/catcoin.tsx',
		/******** phony (make exports) ********/
		[PHONY]: './src/.phony.ts',
	},
	makeAbsoluteExternalsRelative: false,
	plugins: [
		commonjs(),
		nodeResolve({
			browser: true,
		}),
		replace({
			objectGuards: true,
			preventAssignment: true,
			sourceMap: true,
			values: {
				'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
			},
		}),
		esbuild({
			exclude: [],
			jsx: 'transform',
			jsxFactory: 'createElement',
			jsxFragment: 'Fragment',
			loaders: { '.js': false },
			target: 'esnext',
		}),
		new Kitsune,
	],
	output: {
		assetFileNames: assetInfo => {
			switch (extname(assetInfo.name)) {
				case '.ttf': return 'fonts/[name][extname]';
				case '.jpg':
				case '.png': return 'images/[name][extname]';
				default: return 'assets/[name][extname]';
			}
		},
		chunkFileNames: chunkInfo => {
			if (chunkInfo.name.endsWith('@css')) {
				return `css/${chunkInfo.name.slice(0, -4)}.[hash].css`;
			}
			return 'js/[name].[hash].mjs';
		},
		compact: process.env.NODE_ENV === 'production',
		dir: './dist',
		entryFileNames: 'js/pages/[name].[hash].mjs',
		format: 'es',
		generatedCode: 'es2015',
		// hoistTransitiveImports: true,
		importAttributesKey: 'with',
		manualChunks: (id, { getModuleInfo }) => getModuleInfo(id).isEntry ? null : splitRule(id) + (id.endsWith('.css') ? '@css' : ''),
		minifyInternalExports: false,
		plugins: [
			process.env.NODE_ENV === 'production' && minify({
				charset: 'utf8',
				legalComments: 'none',
				treeShaking: true,
			})
		],
		sourcemap: true,
		sourcemapDebugIds: true,
	}
}));
