{"imports": {"katex": "/ajax/libs/KaTeX/0.16.22/katex.mjs", "katex@css": "/ajax/libs/KaTeX/0.16.22/katex.min.css", "monaco-editor/": "https://esm.sh/v135/monaco-editor@0.52.2&no-bundle/", "monaco-editor@css": "/ajax/libs/monaco-editor/0.52.2/min/vs/editor/editor.main.css", "prop-types": "https://esm.sh/prop-types", "react": "https://esm.sh/react", "react-dom": "https://esm.sh/react-dom", "react-dom/client": "https://esm.sh/react-dom/client", "semantic@css": "/ajax/libs/fomantic-ui/2.9.4/semantic.min.css", "semantic-ui-react": "https://esm.sh/semantic-ui-react@%5E3.0.0-beta.2?deps=react@^19,react-dom@^19,react-is@^19&standalone", "socket.io-client": "/ajax/libs/socket.io/4.8.1/socket.io.esm.min.js", "https://esm.sh/node/buffer.mjs": "/js/polyfill/buffer.mjs", "https://esm.sh/node/process.mjs": "/js/polyfill/process.mjs", "index": "/js/pages/index.Cp0lmtBc.mjs", "libs": "/js/libs.C8RNRDyx.mjs", "logout": "/js/pages/logout.EBJkrQKj.mjs", "preference": "/js/pages/preference.CldUOSTe.mjs", "rot10451": "/js/pages/rot10451.CHQ18Plu.mjs", "chordle/list": "/js/pages/chordle/list.Cdbh8IJs.mjs", "genkey": "/js/pages/genkey.Cu8XPPFA.mjs", "login": "/js/pages/login.DjT1Ln2o.mjs", "register": "/js/pages/register.B3Xb3fMn.mjs", "change": "/js/pages/change.CoKoQSpA.mjs", "resetkey": "/js/pages/resetkey.b387hi5p.mjs", "chordle": "/js/pages/chordle.Tb0TfIfB.mjs", "leaderboard": "/js/leaderboard.CqoCxodv.mjs", "ourtimes": "/js/pages/ourtimes.4-pAo6H3.mjs", "catcoin": "/js/catcoin.BWiZg1NM.mjs", "hcaptcha": "/js/hcaptcha.EeVA_0km.mjs", "libs@css": "/css/libs.CeHSMJsK.css", "monaco": "/js/monaco.DE22ZTln.mjs", "monaco@css": "/css/monaco.COck83Sq.css", "socket": "/js/socket.oqqKHRmU.mjs", "react-katex": "/js/react-katex.B7hGdf_Q.mjs", "wasm": "/js/wasm.CPVTfvhq.mjs"}, "scopes": {"https://esm.sh/v135/": {"https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/actionbar/actionbar.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/aria/aria.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/button/button.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/codicons/codicon/codicon-modifiers.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/codicons/codicon/codicon.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/contextview/contextview.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/countBadge/countBadge.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/dropdown/dropdown.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/findinput/findInput.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/hover/hoverWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/iconLabel/iconlabel.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/inputbox/inputBox.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/keybindingLabel/keybindingLabel.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/list/list.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/mouseCursor/mouseCursor.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/progressbar/progressbar.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/sash/sash.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/scrollbar/media/scrollbars.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/selectBox/selectBox.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/selectBox/selectBoxCustom.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/splitview/splitview.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/table/table.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/toggle/toggle.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/toolbar/toolbar.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/base/browser/ui/tree/media/tree.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/controller/textAreaHandler.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/services/hoverService/hover.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/blockDecorations/blockDecorations.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/decorations/decorations.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/glyphMargin/glyphMargin.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/indentGuides/indentGuides.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/lineNumbers/lineNumbers.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/lines/viewLines.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/linesDecorations/linesDecorations.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/margin/margin.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/marginDecorations/marginDecorations.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/minimap/minimap.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/overlayWidgets/overlayWidgets.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/rulers/rulers.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/scrollDecoration/scrollDecoration.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/selections/selections.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/viewCursors/viewCursors.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/viewParts/whitespace/whitespace.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/widget/codeEditor/editor.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/widget/diffEditor/components/accessibleDiffViewer.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/widget/diffEditor/style.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/widget/markdownRenderer/browser/renderedMarkdown.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/browser/widget/multiDiffEditor/style.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/bracketMatching/browser/bracketMatching.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/codeAction/browser/lightBulbWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/colorPicker/browser/colorPicker.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/dropOrPasteInto/browser/postEditWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/find/browser/findOptionsWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/find/browser/findWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/folding/browser/folding.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/gotoError/browser/media/gotoErrorWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/hover/browser/hover.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/inlineCompletions/browser/hintsWidget/inlineCompletionsHintsWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/inlineProgress/browser/inlineProgressWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/message/browser/messageController.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/peekView/browser/media/peekViewWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/symbolIcons/browser/symbolIcons.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/wordHighlighter/browser/highlightDecorations.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/contrib/zoneWidget/browser/zoneWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/standalone/browser/quickInput/standaloneQuickInput.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/editor/standalone/browser/standalone-tokens.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/platform/actionWidget/browser/actionWidget.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/platform/actions/browser/menuEntryActionViewItem.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/platform/quickinput/browser/media/quickInput.css.bundless.js": "/js/polyfill/empty.mjs", "https://esm.sh/v135/monaco-editor@0.52.2/es2022/esm/vs/platform/severityIcon/browser/media/severityIcon.css.bundless.js": "/js/polyfill/empty.mjs"}}, "integrity": {"/ajax/libs/KaTeX/0.16.22/katex.min.css": "sha256-GQlRJzV+1tKf4KY6awAMkTqJ9/GWO3Zd03Fel8mFLnU=", "/ajax/libs/KaTeX/0.16.22/katex.mjs": "sha256-evGqXkhKSuiB4YH/l93/3OLeq+9CWW0meWWLn+TNRhU=", "/ajax/libs/fomantic-ui/2.9.4/semantic.min.css": "sha256-xlp3XbXCVlcKYMpXDnziC1QzN9j0SQiDjRiZ0qu6IOc=", "/js/polyfill/buffer.mjs": "sha256-RtiudSaWkLYhefdnsPMDejSmDkph66IT4I0Vk/SMTQE=", "/js/polyfill/es-arraybuffer-base64.mjs": "sha256-bOezF+Z47TCBTuteuh59UONt4sxE/wfYJWA2KY8ARrI=", "/js/polyfill/process.mjs": "sha256-VuwOUqPnkl9uyONEZ4V+TIcAUVanACvDPVCdReNzKE4=", "/js/polyfill/webassembly-function.mjs": "sha256-hiyZoW+DgOyMFu/YyOzCn/DClLfRzhj29vLf8WqqZ/4=", "/js/polyfill/websocketstream.mjs": "sha256-wy2x23eTB6tQWCiy4yMCKY++DpUKp8shr0HVRUAWfzg=", "/js/pages/index.Cp0lmtBc.mjs": "sha256-xBqiW3XgivZRgU8J/JKrOs/GlyLplvb7919ETShScTQ=", "/js/pages/logout.EBJkrQKj.mjs": "sha256-7g3YZGnEMhJe5i42fHyy3QNboYnV8z9sTSoWbqOPN9w=", "/js/pages/preference.CldUOSTe.mjs": "sha256-WC4qn4cm8m9I2us7RraMsz3FlG20JyWwRqPDX/wxZxs=", "/js/pages/rot10451.CHQ18Plu.mjs": "sha256-xpJxjgY5VnxV8jkJLLQXfK9dPvRGekbcluXmPTAGkR8=", "/js/pages/chordle/list.Cdbh8IJs.mjs": "sha256-oi3kgvn1M4fnZeHS7ppKJu4LQ7zPYKelszgjw4V5OfI=", "/js/pages/genkey.Cu8XPPFA.mjs": "sha256-SgGoLXDntNdLHOWYCTcbNkbFe82g4gfx6di7Y1Qzaxw=", "/js/pages/login.DjT1Ln2o.mjs": "sha256-DnQpo+egH7wqiNHq+w4OOS4zGM9DL/ZdgiKCbROaOOk=", "/js/pages/register.B3Xb3fMn.mjs": "sha256-VR8anpmkA/OlO2o6JU1GYYLIHwxlrF43DwICN6JRL/s=", "/js/pages/change.CoKoQSpA.mjs": "sha256-OkjPfhbwuVUAo1UcWRr4vnAyadDdBRCiNPYvhZh7Q6k=", "/js/pages/resetkey.b387hi5p.mjs": "sha256-cVMw+7nKN35kBdAqSjbU8D2jo+YlA7l34GS7JcmQRwA=", "/js/pages/chordle.Tb0TfIfB.mjs": "sha256-fl8LMN6L9suUUQm1C2uR2saVoIy9/L2vgjIUz6XXqt0=", "/js/pages/leaderboard.BEj67dXv.mjs": "sha256-P+C0TrqMHPF6ftNrLXpXoIcC/n5c1N6lVtSW7c+n6w8=", "/js/pages/ourtimes.4-pAo6H3.mjs": "sha256-xwurC4fHbkxkFOqT/ps4QSAFAHNAofps5GG+4iKKtKc=", "/js/pages/catcoin.CxZie1uy.mjs": "sha256-DcTKQtQThP7ozA88VYeYyaXSYknkvaEoJS5FkymN1+I=", "/js/catcoin.BWiZg1NM.mjs": "sha256-CfICSF6Z0REGyE0AgFPzZkQ9mdGjKr7o/8gpnDc5TnQ=", "/js/hcaptcha.EeVA_0km.mjs": "sha256-f5HRys37GvRBuo2HU34XpFfvHBgLFcCXqsYEALBrI+8=", "/js/leaderboard.CqoCxodv.mjs": "sha256-CUp588JpACBxt2P1BL6bTq9RP2umW+f4hj6S/e5BAiw=", "/js/libs.C8RNRDyx.mjs": "sha256-lk8ruffoGDCzpIemnv4TQMg9yk1LVCenmzofvp9b6XM=", "/css/libs.CeHSMJsK.css": "sha256-btB4E3m/b+2MSoe6yApA6PyjLsULZIlYD2r7XR2UzIg=", "/js/monaco.DE22ZTln.mjs": "sha256-0U+zr0sdRQwsbZK/Au0zLeDdtdXRzYcQpyEk7xZ2tyI=", "/css/monaco.COck83Sq.css": "sha256-ZXd+Elpsj8mM6cHROfeijMdHNpAWdZvU1cVanaTM3go=", "/js/react-katex.B7hGdf_Q.mjs": "sha256-9j9eS4ybN0wV2U3gE+3o+NJ0kGAPuwI3L3Tq3xGanaY=", "/js/socket.oqqKHRmU.mjs": "sha256-On4LMt7mUpwytk1rrHcvrNnBo1iNUqSqeNLuq/112bo=", "/js/wasm.CPVTfvhq.mjs": "sha256-DzuVe2OTghPOI0OwZUYe1l6ttZE+7PFMUkBza5jaYF0="}, "pages": [{"route": "/", "script": "\n\t\t\timport root from 'index';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "首页"}, {"route": "/logout", "script": "\n\t\t\timport 'logout';\n\t\t", "title": "登出"}, {"route": "/preference", "script": "\n\t\t\timport root from 'preference';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "偏好设置"}, {"route": "/rot10451", "script": "\n\t\t\timport root from 'rot10451';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "在线 rot10451 编码"}, {"route": "/chordle/list", "script": "\n\t\t\timport root from 'chordle/list';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "Chordle 谜题列表"}, {"route": "/genkey", "script": "\n\t\t\timport root from 'genkey';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "在线密钥生成"}, {"route": "/login", "script": "\n\t\t\timport root from 'login';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "登录信息配置"}, {"route": "/register", "script": "\n\t\t\timport root from 'register';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "注册"}, {"route": "/change", "script": "\n\t\t\timport root from 'change';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "修改用户信息"}, {"route": "/resetkey", "script": "\n\t\t\timport root from 'resetkey';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "重置密钥"}, {"route": "/chordle", "script": "\n\t\t\timport root from 'chordle';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "Chordle"}, {"route": "/sast2022/leaderboard", "script": "\n\t\t\timport root from 'leaderboard';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "PyTorch Leaderboard"}, {"route": "/我的少女时代", "script": "\n\t\t\timport root from 'ourtimes';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "我的少女时代"}, {"route": "/catcoin", "script": "\n\t\t\timport root from 'catcoin';\n\t\t\timport { renderRoot } from 'libs';\n\t\t\trenderRoot(root);\n\t\t", "title": "Catcoin"}]}