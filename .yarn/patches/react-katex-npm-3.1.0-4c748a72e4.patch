diff --git a/package.json b/package.json
index 6ef236fa15bebdd7afafc91d2d948c9d8a05943e..9a886cab5964d5d1e248ad89aaeccfe7915764b2 100644
--- a/package.json
+++ b/package.json
@@ -10,7 +10,7 @@
     "katex"
   ],
   "homepage": "https://github.com/talyssonoc/react-katex",
-  "main": "dist/react-katex.js",
+  "main": "src/index.jsx",
   "scripts": {
     "build": "NODE_ENV=production swc ./src/index.jsx -o dist/react-katex.js",
     "test": "jest",
diff --git a/src/index.jsx b/src/index.jsx
index 0a207a4af2af2ed730fb09707abf7337b9117d61..c77584024ce98e7948b8ebb5725bc5975cbd8a7b 100644
--- a/src/index.jsx
+++ b/src/index.jsx
@@ -1,6 +1,7 @@
-import React, { useMemo } from 'react';
+import { createElement, useMemo } from 'react';
 import PropTypes from 'prop-types';
 import KaTeX from 'katex';
+import KaTeXStyle from 'katex@css' with { type: 'css' };
 
 /**
  * @typedef {import("react").ReactNode} ReactNode
@@ -27,6 +28,8 @@ import KaTeX from 'katex';
  * @typedef {MathComponentPropsWithMath | MathComponentPropsWithChildren} MathComponentProps
  */
 
+document.adoptedStyleSheets.push(KaTeXStyle);
+
 const createMathComponent = (Component, { displayMode }) => {
   /**
    *
