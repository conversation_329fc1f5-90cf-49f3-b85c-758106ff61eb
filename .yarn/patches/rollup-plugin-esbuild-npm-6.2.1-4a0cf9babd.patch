diff --git a/dist/index.mjs b/dist/index.mjs
index 9623ef40cb9c5007d6c942bc03fbfe44fb4fa0c4..98dd408477b6b62a2ac4513443c405266c04b768 100644
--- a/dist/index.mjs
+++ b/dist/index.mjs
@@ -32,10 +32,10 @@ var getEsbuildFormat = (rollupFormat) => {
 var getRenderChunk = ({
   sourceMap = true,
   ...options
-}) => async function(code, _, rollupOptions) {
-  if (options.minify || options.minifyWhitespace || options.minifyIdentifiers || options.minifySyntax) {
+}) => async function(code, chunk, rollupOptions) {
+  if ((options.minify || options.minifyWhitespace || options.minifyIdentifiers || options.minifySyntax) && !chunk.name.endsWith('@css')) {
     const format = getEsbuildFormat(rollupOptions.format);
-    const result = await transform(code, {
+    const result = await transform(code + 'export{}', {
       format,
       loader: "js",
       sourcemap: sourceMap,
@@ -246,6 +246,7 @@ var index_default = ({
       if (!filter(id) || (optimizeDepsResult == null ? void 0 : optimizeDepsResult.optimized.has(id))) {
         return null;
       }
+      console.log(`\x1b[36mTransforming \x1b[33m${id}\x1b[36m ...\x1b[0m`);
       const ext = extname(id);
       const loader = loaders[ext];
       if (!loader) {
