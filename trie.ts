import assert from 'assert';

const magicChar = '\ufefe';

export function isObject(value) {
	return value?.constructor === Object && Object.prototype.toString.call(value) === '[object Object]' && Object.getPrototypeOf(value) === Object.prototype;
}

function replacer(key, value) {
	return isObject(value) ? Object.fromEntries(Object.entries(value).map(([k, v]) => [magicChar + k, v]).sort()) : value;
}

function sortedStringify(value) {
	return JSON.stringify(value, replacer).replaceAll(magicChar, '');
}

function insertTrie(trie, key, value) {
	for (const char of key) trie = trie[char] ??= {};
	trie.vl = value;
}

function simplifyTrie(trie) {
	const childs = Object.keys(trie).filter(x => x.length === 1), z = new Set();
	for (const child of childs) z.add(simplifyTrie(trie[child]));
	if (Object.hasOwn(trie, 'vl')) z.add(trie.vl);
	assert.notStrictEqual(z.size, 0);
	if (z.size === 1) {
		const vl = z.keys().next().value;
		if (vl !== null) {
			for (const child of childs) delete trie[child];
			delete trie.su;
			return trie.vl = vl;
		}
	}
	if (Object.hasOwn(trie, 'vl')) return null;
	if (childs.length === 1) {
		const node = trie[childs[0]];
		delete trie[childs[0]];
		for (const k in node) trie[k] = node[k];
		trie.su = childs[0] + (trie.su ?? '');
		return null;
	}
	assert(childs.length > 1);
	return null;
}

export function genTrie(map) {
	const list = [...map.keys()].sort(), trie = {};
	const listCode = `JSON.parse(${JSON.stringify(JSON.stringify(list))})`;
	list.forEach((fn, id) => {
		for (const module of map.get(fn))
			insertTrie(trie, module, id);
	});
	simplifyTrie(trie);
	const trieCode = `JSON.parse(${JSON.stringify(sortedStringify(trie))})`;
	return { listCode, trieCode };
}
