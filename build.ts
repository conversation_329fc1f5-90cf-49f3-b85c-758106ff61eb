import assert from 'assert';
import { createHash } from 'crypto';
import { transform } from 'esbuild';
import { readFileSync } from 'fs';
import { cp, readFile, rm, writeFile } from 'fs/promises';
import MagicString, { Bundle, SourceMap } from 'magic-string';
import { basename, dirname, join, relative, resolve } from 'path';
import { rollup } from 'rollup';
import { batchWarnings } from 'rollup/dist/shared/loadConfigFile.js';
import { collapseSourcemap, decodedSourcemap, handleError, mergeOptions } from 'rollup/dist/shared/rollup.js';
import { fileURLToPath } from 'url';
import { inspect } from 'util';

import Config from './rollup.config.ts';
import { genTrie } from './trie.ts';

const
	ROOT = getRoot(),
	importMapUpdates = new Map(),
	integrityUpdates = new Map();

export function getRoot() {
	return dirname(fileURLToPath(import.meta.url));
}

function initStaticResources() {
	const opt = { force: true, recursive: true };
	return Promise.all([
		rm(resolve(ROOT, 'dist/assets'), opt),
		rm(resolve(ROOT, 'dist/css'), opt),
		rm(resolve(ROOT, 'dist/fonts'), opt),
		rm(resolve(ROOT, 'dist/js'), opt),
		rm(resolve(ROOT, 'dist/images'), opt),
	]);
}

function updateSourceMap(sourceMap, absoluteDirName) {
	const { sources, sourcesContent } = sourceMap;
	assert.equal(sources.length, sourcesContent.length);
	for (let i = 0; i < sourceMap.sources.length; ++i) {
		sources[i] = relative(ROOT, resolve(absoluteDirName, sources[i]));
		if (sources[i].startsWith('src/'))
			sourcesContent[i] = null;
	}
	sourceMap.sourceRoot = '/';
}

const
	codeMap = new Map(),
	mapMap = new Map(),
	MODULE_TRIE = 'module-trie',
	PHONY = '_phony_magic_xv1x';
let jkmx$bundleObject, jkmx$chunksObject;
/******** WILL USE ESBUILD ENTIRELY ONCE https://github.com/evanw/esbuild/issues/207 IS RESOLVED ********/
export class Kitsune {
	name = 'Kitsune';

	resolveId(source) { // module-trie
		if (source === MODULE_TRIE) return source;
		return null;
	}

	load(id) { // module-trie
		if (id === MODULE_TRIE) {
			return {
				code: `\
export const moduleList = [ /* builtin-code value */ ];
export const moduleTrie = { /* builtin-code value */ };
`
			};
		}
		return null;
	}

	transform(code, id) { // css
		if (id.endsWith('.css')) {
			codeMap.set(id, code);
			return { code: 'export default import.meta.kiriha;', map: null };
		}
		return null;
	}

	resolveImportMeta(property) { // zustand opt
		if (property === 'env') {
			return `({ MODE: ${JSON.stringify(process.env.NODE_ENV)} })`;
		}
		return null;
	}

	async renderChunk(_, chunk) { // css
		if (!chunk.name.endsWith('@css')) return null;

		const mergedBundle = new Bundle();
		for (const file of Object.keys(chunk.modules)) {
			const
				source = codeMap.get(file),
				content = new MagicString(source);
			for (const { 1: match, indices: { 1: [l, r] } } of source.matchAll(/\burl\((.+?)\)/dg)) {
				if (match.includes('data:')) continue;
				const url = resolve(dirname(file), match);
				const id = this.emitFile({
					name: basename(match),
					source: readFileSync(url),
					type: 'asset',
				});
				const fn = this.getFileName(id);
				const replacement = join('..', fn);
				content.update(l, r, replacement);
			}
			mergedBundle.addSource({
				filename: file,
				content,
				ignoreList: file.includes('node_modules'),
			});
		}

		const source = mergedBundle.toString();
		let code = source, map, sourceMap;

		if (process.env.NODE_ENV === 'production') {
			({ code, map } = await transform(source, {
				loader: 'css',
				sourcemap: true,
				charset: 'utf8',
				legalComments: 'none',
				treeShaking: true,
				minify: true,
			}));
		}

		const
			absoluteFileName = resolve(ROOT, chunk.fileName),
			sourceMapRaw = mergedBundle.generateDecodedMap({
				file: chunk.fileName,
				hires: true,
				includeContent: true
			});

		if (process.env.NODE_ENV === 'production') {
			// compose source-map
			const fullMapping = collapseSourcemap(
				absoluteFileName,
				source,
				sourceMapRaw,
				[decodedSourcemap(map)],
				warning => { debugger; }
			);
			sourceMap = new SourceMap(fullMapping);
			sourceMap.file = sourceMapRaw.file;
		} else {
			sourceMap = new SourceMap(sourceMapRaw);
		}

		mapMap.set(chunk.name, sourceMap);

		return { code, map: sourceMap };
	}

	async generateBundle(_, bundle) { // css
		for (const chunkName in bundle) {
			const chunk = bundle[chunkName];
			if (chunk.type === 'chunk' && chunk.name.endsWith('@css')) {
				const lines = chunk.code.split('\n');
				for (let i = lines.length - 2; i < lines.length; ++i) {
					const line = lines[i];
					assert(line.startsWith('//# '));
					lines[i] = `/*# ${line.substring(4)} */`;
				}
				chunk.code = lines.join('\n');
			}
		}
	}

	jkmx$updateChunkMap(sourceMap, fileName, chunk) { // css + source-map
		updateSourceMap(sourceMap, dirname(fileName));
		if (fileName.endsWith('.css')) {
			sourceMap.mappings = mapMap.get(chunk.name).mappings;
		}
	}

	async jkmx$postGenerate(bundle, chunks) { // init
		jkmx$bundleObject = bundle;
		jkmx$chunksObject = chunks;
		const phonyPos = chunks.findIndex(chunk => chunk.name === PHONY);
		if (phonyPos >= 0) chunks.splice(phonyPos, 1);
	}

	jkmx$postRenderModule(source, module) { // module-trie
		console.log(`\x1b[36mRendered \x1b[33m${module.id}\x1b[36m !\x1b[0m`);
		if (module.id === MODULE_TRIE) {
			const map = new Map();
			for (const chunk of jkmx$chunksObject) {
				const modules = new Set();
				for (const { id } of chunk.orderedModules) {
					if (id.startsWith(ROOT)) {
						const t = relative(ROOT, id);
						modules.add(t.startsWith('node_modules/') ? t.substring(13) : t);
					}
				}
				if (modules.size) {
					const fn = `/${chunk.getFileName()}`;
					map.set(fn, modules);
				}
			}
			const { listCode, trieCode } = genTrie(map);
			source.update(26, 54, listCode);
			source.update(82, 110, trieCode);
		}
	}
};

async function build() {
	const warnings = batchWarnings({});
	try {
		return await rollup(await mergeOptions(await Config, {}, warnings.add));
	} catch (e) {
		warnings.flush();
		handleError(e);
		throw e;
	}
}

async function emit(bundle) {
	const { output } = await bundle.write((await Config).output);
	output.forEach(chunk => {
		const content = chunk.code ?? chunk.source;
		if (content && !chunk.fileName.endsWith('.map')) {
			const digest = createHash('sha256').update(content).digest('base64');
			integrityUpdates.set(chunk.fileName, `sha256-${digest}`);
		}
		importMapUpdates.set(chunk.name, chunk.fileName);
	});
	if (bundle) {
		await bundle.close();
	}
	await cp(resolve(ROOT, 'public/dist:js:polyfill'), resolve(ROOT, 'dist/js/polyfill'), { recursive: true, verbatimSymlinks: true });
}

async function doUpdateConfig() {
	const removeHash = str => str.replace(/\.[0-9a-zA-Z_-]+\./, '.');
	const integrityUpdates_ = new Set(integrityUpdates.entries().map(([x]) => removeHash(x)));

	const config = JSON.parse(await readFile('config.json'));

	for (const [file, map] of importMapUpdates.entries())
		if (typeof file === 'string')
			config.imports[file] = `/${map}`;

	for (const file in config.integrity) {
		const key = removeHash(file);
		if (key[0] === '/' && integrityUpdates_.has(key.substring(1)))
			delete config.integrity[file];
	}

	for (const [file, integrity] of integrityUpdates)
		config.integrity[`/${file}`] = integrity;

	await writeFile('config.json', JSON.stringify(config, null, '\t') + '\n');
}

if (process.argv.length > 1 && resolve(process.argv[1]) === fileURLToPath(import.meta.url)) {
	try {
		if (!['development', 'production'].includes(process.env.NODE_ENV)) {
			throw new Error(`Please specify compile environment (NODE_ENV): expected \x1b[33m'development'\x1b[0m/\x1b[33m'production'\x1b[0m, found ${inspect(process.env.NODE_ENV, { colors: true })})`);
		}
		const t0 = process.uptime();
		console.log('\n\x1b[36mDeleting cached assets ...\x1b[0m\n');
		await initStaticResources();
		console.log(`\x1b[36mBuilding (\x1b[32m'${process.env.NODE_ENV}'\x1b[36m mode) ...\x1b[0m\n`);
		const result = await build();
		console.log('\n\x1b[36mEmitting ...\x1b[0m\n');
		await emit(result);
		console.log('\n\x1b[36mUpdating \x1b[33mconfig.json\x1b[36m ...\x1b[0m\n');
		await doUpdateConfig();
		console.log(`\x1b[32mFinished in \x1b[33m${process.uptime() - t0}\x1b[32m s !`);
	} catch (e) {
		console.log(e);
	}
}

export { PHONY };
